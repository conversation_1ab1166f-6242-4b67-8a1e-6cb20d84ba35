from pydantic import BaseModel, EmailStr
from typing import Optional
from datetime import datetime
from .responses import StandardResponse, PaginatedResponse

class UserBase(BaseModel):
    full_name: str
    email: EmailStr
    username: str
    role_id: str
    tenant_id: Optional[str] = None

class UserCreate(UserBase):
    password: str

class UserUpdate(BaseModel):
    full_name: Optional[str] = None
    email: Optional[EmailStr] = None
    role_id: Optional[str] = None
    tenant_id: Optional[str] = None

class UserResponse(UserBase):
    id: str
    keycloak_id: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    model_config = {"from_attributes": True}

# Type aliases for standardized responses
UserStandardResponse = StandardResponse[UserResponse]
UserListStandardResponse = PaginatedResponse[UserResponse]
DeleteStandardResponse = StandardResponse[dict]