from sqlalchemy import Column, String, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from datetime import datetime
from .base import BaseModel

class UserPermission(BaseModel):
    __tablename__ = "user_permissions"
    
    user_id = Column(String, ForeignKey("users.id"), nullable=False)  # Now references local user table
    permission_id = Column(String, ForeignKey("permissions.id"))
    granted_at = Column(DateTime, default=datetime.utcnow)
    granted_by = Column(String)
    tenant_id = Column(String, ForeignKey("tenants.id"), nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="user_permissions")
    permission = relationship("Permission")
    tenant = relationship("Tenant")