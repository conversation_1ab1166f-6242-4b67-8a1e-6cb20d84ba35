#!/usr/bin/env python3
"""
Test script to verify permissions seeding functionality
"""
import requests
import json

BASE_URL = "http://localhost:8000/api"

def test_permissions_seed():
    """Test the permissions seeding endpoint"""
    print("Testing permissions seeding...")
    
    try:
        # Test seed status first
        response = requests.get(f"{BASE_URL}/seed/status")
        if response.status_code == 200:
            status = response.json()
            print(f"Current seed status: {json.dumps(status, indent=2)}")
        
        # Test permissions seeding
        response = requests.post(f"{BASE_URL}/seed/permissions")
        
        if response.status_code == 200:
            permissions = response.json()
            print(f"✅ Successfully seeded {len(permissions)} permissions")
            
            # Print the first few permissions as example
            print("\nSample permissions created:")
            for perm in permissions[:5]:
                print(f"  - {perm['resource']}.{perm['action']}: {perm['name']}")
            
            if len(permissions) > 5:
                print(f"  ... and {len(permissions) - 5} more")
                
        else:
            print(f"❌ Failed to seed permissions: {response.status_code}")
            print(response.text)
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to the API. Make sure the server is running on http://localhost:8000")
    except Exception as e:
        print(f"❌ Error testing permissions seed: {str(e)}")

def test_seed_all():
    """Test seeding all data"""
    print("\nTesting full seed (all data)...")
    
    try:
        response = requests.post(f"{BASE_URL}/seed/all")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Successfully ran all seeds:")
            for key, value in result.items():
                print(f"  - {key}: {value}")
        else:
            print(f"❌ Failed to run all seeds: {response.status_code}")
            print(response.text)
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to the API. Make sure the server is running on http://localhost:8000")
    except Exception as e:
        print(f"❌ Error testing full seed: {str(e)}")

def test_admin_permissions():
    """Test assigning all permissions to admin user"""
    print("\nTesting admin permissions assignment...")
    
    try:
        response = requests.post(f"{BASE_URL}/seed/admin-permissions")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Successfully assigned permissions to admin:")
            print(f"  - User: {result.get('user', 'N/A')}")
            print(f"  - Permissions assigned: {result.get('permissions_assigned', 'N/A')}")
            print(f"  - Message: {result.get('message', 'N/A')}")
        else:
            print(f"❌ Failed to assign admin permissions: {response.status_code}")
            print(response.text)
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to the API. Make sure the server is running on http://localhost:8000")
    except Exception as e:
        print(f"❌ Error testing admin permissions: {str(e)}")

if __name__ == "__main__":
    print("=" * 50)
    print("Testing Permissions Seed Functionality")
    print("=" * 50)
    
    test_permissions_seed()
    test_seed_all()
    test_admin_permissions()
    
    print("\n" + "=" * 50)
    print("Test completed!")