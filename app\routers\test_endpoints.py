from fastapi import APIRouter, Depends
from ..services.tenant_service import get_tenant_service, TenantService
from ..services.user_service import get_user_service, UserService
from ..services.school_service import get_school_service, SchoolService
from ..services.permission_service import PermissionService
from ..services.role_service import RoleService
from ..utils.responses import success_response, paginated_response

router = APIRouter(prefix="/test", tags=["testing"])

@router.get("/roles")
async def get_roles_no_auth():
    """Test endpoint without authentication"""
    return {
        "roles": [
            {"id": "1", "name": "Admin", "code": "ADMIN"},
            {"id": "2", "name": "User", "code": "USER"}
        ]
    }

@router.get("/users")
async def get_users_no_auth(
    page: int = 1,
    size: int = 20,
    user_service: UserService = Depends(get_user_service)
):
    """Test users endpoint without authentication"""
    skip = (page - 1) * size
    users = await user_service.get_users(skip=skip, limit=size)
    total = await user_service.get_total_users_count()
    
    return paginated_response(
        data=users,
        total=total,
        page=page,
        size=size,
        message="Users retrieved successfully (test mode)"
    )

@router.get("/generate-token")
async def generate_test_token():
    """Generate a test token for development"""
    import jwt
    from datetime import datetime, timedelta
    
    payload = {
        'sub': 'test-user-123',
        'username': 'testuser',
        'exp': datetime.utcnow() + timedelta(hours=1),
        'iat': datetime.utcnow()
    }
    token = jwt.encode(payload, 'test-secret', algorithm='HS256')
    
    return {
        "access_token": token,
        "token_type": "bearer",
        "expires_in": 3600,
        "usage": f"curl -H 'Authorization: Bearer {token}' http://localhost:8000/test/protected"
    }

@router.get("/protected")
async def protected_endpoint():
    """Test protected endpoint (requires manual token validation)"""
    return {"message": "This would normally require authentication"}

@router.get("/tenants")
async def get_tenants_no_auth(
    page: int = 1,
    size: int = 20,
    tenant_service: TenantService = Depends(get_tenant_service)
):
    """Test tenants endpoint without authentication"""
    skip = (page - 1) * size
    tenants = await tenant_service.get_tenants(skip=skip, limit=size)
    total = await tenant_service.get_total_tenants_count()
    
    return paginated_response(
        data=tenants,
        total=total,
        page=page,
        size=size,
        message="Tenants retrieved successfully (test mode)"
    )

@router.get("/schools")
async def get_schools_no_auth(
    page: int = 1,
    size: int = 20,
    school_service: SchoolService = Depends(get_school_service)
):
    """Test schools endpoint without authentication (no tenant filtering)"""
    skip = (page - 1) * size
    # Note: This bypasses tenant filtering for testing
    schools = await school_service.get_schools(skip=skip, limit=size, tenant_id=None)
    total = await school_service.get_total_schools_count(tenant_id=None)
    
    return paginated_response(
        data=schools,
        total=total,
        page=page,
        size=size,
        message="Schools retrieved successfully (test mode - no tenant filter)"
    )

@router.get("/permissions")
async def get_permissions_no_auth(
    permission_service: PermissionService = Depends()
):
    """Test permissions endpoint without authentication"""
    permissions = permission_service.get_permissions()
    return success_response(
        data=[{"id": p.id, "name": p.name, "resource": p.resource, "action": p.action} for p in permissions],
        message="Permissions retrieved successfully (test mode)"
    )

@router.get("/roles-db")
async def get_roles_from_db_no_auth(
    role_service: RoleService = Depends()
):
    """Test roles endpoint without authentication - from database"""
    roles = role_service.get_roles()
    return success_response(
        data=[{"id": r.id, "name": r.name, "code": r.code} for r in roles],
        message="Roles retrieved successfully (test mode)"
    )