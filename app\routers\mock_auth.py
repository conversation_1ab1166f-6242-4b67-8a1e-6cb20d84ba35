from fastapi import APIRouter, HTTPException
from ..schemas.auth import LoginRequest, TokenResponse
import jwt
from datetime import datetime, timedelta

router = APIRouter(prefix="/mock-auth", tags=["mock-authentication"])

# Mock JWT secret (use proper secret in production)
SECRET_KEY = "mock-secret-key-for-testing-only"
ALGORITHM = "HS256"

@router.post("/login", response_model=TokenResponse)
async def mock_login(login_data: LoginRequest):
    # Mock user validation (accept any username/password for testing)
    if login_data.username and login_data.password:
        # Create mock JWT token
        payload = {
            "sub": "mock-user-id-123",
            "username": login_data.username,
            "exp": datetime.utcnow() + timedelta(hours=1),
            "iat": datetime.utcnow()
        }
        
        access_token = jwt.encode(payload, SECRET_KEY, algorithm=ALGORITHM)
        refresh_token = jwt.encode({**payload, "type": "refresh"}, SECRET_KEY, algorithm=ALG<PERSON>ITHM)
        
        return TokenResponse(
            access_token=access_token,
            refresh_token=refresh_token,
            token_type="bearer",
            expires_in=3600
        )
    
    raise HTTPException(status_code=401, detail="Invalid credentials")