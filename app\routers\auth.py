from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from ..schemas.auth import LoginRequest, TokenResponse, RefreshTokenRequest, LogoutResponse
from ..services.keycloak_service import keycloak_service
from ..utils.responses import success_response
from ..schemas.responses import StandardResponse

import logging
logger = logging.getLogger("uvicorn.error")


router = APIRouter(prefix="/auth", tags=["authentication"])
security = HTTPBearer()

@router.post("/login", response_model=StandardResponse[TokenResponse])
async def login(login_data: LoginRequest):
    token_data = await keycloak_service.authenticate_user(
        login_data.username, 
        login_data.password
    )

    logger.info(f"token_data: {token_data}")

    token_response = TokenResponse(
        access_token=token_data["access_token"],
        refresh_token=token_data["refresh_token"],
        token_type="bearer",
        expires_in=token_data["expires_in"]
    )
    
    return success_response(data=token_response, message="Login successful")

@router.post("/refresh", response_model=StandardResponse[TokenResponse])
async def refresh_token(refresh_data: RefreshTokenRequest):
    token_data = await keycloak_service.refresh_token(refresh_data.refresh_token)
    token_response = TokenResponse(
        access_token=token_data["access_token"],
        refresh_token=token_data["refresh_token"],
        token_type="bearer",
        expires_in=token_data["expires_in"]
    )
    return success_response(data=token_response, message="Token refreshed successfully")

@router.post("/logout", response_model=StandardResponse[dict])
async def logout(credentials: HTTPAuthorizationCredentials = Depends(security)):
    await keycloak_service.logout_user(credentials.credentials)
    return success_response(data={"logged_out": True}, message="Logout successful")