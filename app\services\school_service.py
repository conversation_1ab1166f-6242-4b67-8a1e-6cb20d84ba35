from fastapi import HTTPException, Depends
from sqlalchemy.orm import Session
from typing import List, Optional
from ..models.school import School
from ..models.user import User
from ..models.tenant import Tenant
from ..database.session import get_db
from ..schemas.school import SchoolCreate, SchoolUpdate, SchoolResponse
from ..utils.responses import APIException, ErrorCodes


class SchoolService:
    def __init__(self, db: Session = Depends(get_db)):
        self.db = db
    
    async def create_school(self, school_data: SchoolCreate) -> SchoolResponse:
        """Create a new school"""
        try:
            # Check if tenant exists
            tenant = self.db.query(Tenant).filter(Tenant.id == school_data.tenant_id).first()
            if not tenant:
                raise APIException(
                    error_code="TENANT_001",
                    message="Tenant not found"
                )
            
            # Check if manager exists
            manager = self.db.query(User).filter(User.id == school_data.manager_id).first()
            if not manager:
                raise APIException(
                    error_code=ErrorCodes.USER_NOT_FOUND,
                    message="Manager not found"
                )
            
            # Check if code already exists within tenant
            existing_school = self.db.query(School).filter(
                School.code == school_data.code,
                School.tenant_id == school_data.tenant_id
            ).first()
            if existing_school:
                raise APIException(
                    error_code="SCHOOL_002",
                    message="School code already exists"
                )
            
            # Create school
            db_school = School(
                name=school_data.name,
                code=school_data.code,
                address=school_data.address,
                phone=school_data.phone,
                tenant_id=school_data.tenant_id,
                manager_id=school_data.manager_id
            )
            
            self.db.add(db_school)
            self.db.commit()
            self.db.refresh(db_school)
            
            return SchoolResponse.model_validate(db_school)
            
        except APIException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Error creating school: {str(e)}")
    
    async def get_school_by_id(self, school_id: str, tenant_id: str) -> SchoolResponse:
        """Get school by ID filtered by tenant"""
        school = self.db.query(School).filter(
            School.id == school_id,
            School.tenant_id == tenant_id
        ).first()
        if not school:
            raise APIException(
                error_code="SCHOOL_001",
                message="School not found"
            )
        
        return SchoolResponse.model_validate(school)
    
    async def get_schools(self, skip: int = 0, limit: int = 20, tenant_id: str = None) -> List[SchoolResponse]:
        """Get list of schools with pagination filtered by tenant"""
        query = self.db.query(School)
        if tenant_id is not None:
            query = query.filter(School.tenant_id == tenant_id)
        schools = query.offset(skip).limit(limit).all()
        
        return [SchoolResponse.model_validate(school) for school in schools]
    
    async def get_total_schools_count(self, tenant_id: str = None) -> int:
        """Get total count of schools filtered by tenant"""
        query = self.db.query(School)
        if tenant_id is not None:
            query = query.filter(School.tenant_id == tenant_id)
        return query.count()
    
    async def update_school(self, school_id: str, school_data: SchoolUpdate, tenant_id: str) -> SchoolResponse:
        """Update school information filtered by tenant"""
        school = self.db.query(School).filter(
            School.id == school_id,
            School.tenant_id == tenant_id
        ).first()
        if not school:
            raise APIException(
                error_code="SCHOOL_001",
                message="School not found"
            )
        
        # Check if manager exists (if manager_id is being updated)
        if school_data.manager_id:
            manager = self.db.query(User).filter(User.id == school_data.manager_id).first()
            if not manager:
                raise APIException(
                    error_code=ErrorCodes.USER_NOT_FOUND,
                    message="Manager not found"
                )
        
        # Check if code already exists within tenant (if code is being updated)
        if school_data.code and school_data.code != school.code:
            existing_school = self.db.query(School).filter(
                School.code == school_data.code,
                School.tenant_id == tenant_id
            ).first()
            if existing_school:
                raise APIException(
                    error_code="SCHOOL_002",
                    message="School code already exists"
                )
        
        # Update school fields
        update_data = school_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(school, field, value)
        
        self.db.commit()
        self.db.refresh(school)
        
        return SchoolResponse.model_validate(school)
    
    async def delete_school(self, school_id: str, tenant_id: str) -> dict:
        """Delete school filtered by tenant"""
        school = self.db.query(School).filter(
            School.id == school_id,
            School.tenant_id == tenant_id
        ).first()
        if not school:
            raise APIException(
                error_code="SCHOOL_001",
                message="School not found"
            )
        
        self.db.delete(school)
        self.db.commit()
        
        return {"message": "School successfully deleted"}


def get_school_service(db: Session = Depends(get_db)) -> SchoolService:
    """Dependency to get SchoolService instance"""
    return SchoolService(db)