from fastapi import HTTPException, Depends
from sqlalchemy.orm import Session
from typing import List, Optional
from ..models.tenant import Tenant
from ..models.user import User
from ..database.session import get_db
from ..schemas.tenant import TenantCreate, TenantUpdate, TenantResponse
from ..utils.responses import APIException, ErrorCodes


class TenantService:
    def __init__(self, db: Session = Depends(get_db)):
        self.db = db
    
    async def create_tenant(self, tenant_data: TenantCreate) -> TenantResponse:
        """Create a new tenant"""
        try:
            # Check if manager exists
            manager = self.db.query(User).filter(User.id == tenant_data.manager_id).first()
            if not manager:
                raise APIException(
                    error_code=ErrorCodes.USER_NOT_FOUND,
                    message="Manager not found"
                )
            
            # Create tenant
            db_tenant = Tenant(
                name=tenant_data.name,
                package=tenant_data.package,
                expired_date=tenant_data.expired_date,
                cycle=tenant_data.cycle,
                manager_id=tenant_data.manager_id,
                phone=tenant_data.phone,
                created_by=tenant_data.created_by
            )
            
            self.db.add(db_tenant)
            self.db.commit()
            self.db.refresh(db_tenant)
            
            return TenantResponse(
                id=db_tenant.id,
                name=db_tenant.name,
                package=db_tenant.package,
                expired_date=db_tenant.expired_date,
                cycle=db_tenant.cycle,
                manager_id=db_tenant.manager_id,
                phone=db_tenant.phone,
                created_by=db_tenant.created_by,
                created_at=db_tenant.created_at,
                updated_at=db_tenant.updated_at
            )
            
        except APIException:
            raise
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Error creating tenant: {str(e)}")
    
    async def get_tenant_by_id(self, tenant_id: str) -> TenantResponse:
        """Get tenant by ID"""
        tenant = self.db.query(Tenant).filter(Tenant.id == tenant_id).first()
        if not tenant:
            raise APIException(
                error_code="TENANT_001",
                message="Tenant not found"
            )
        
        return TenantResponse(
            id=tenant.id,
            name=tenant.name,
            package=tenant.package,
            expired_date=tenant.expired_date,
            cycle=tenant.cycle,
            manager_id=tenant.manager_id,
            phone=tenant.phone,
            created_by=tenant.created_by,
            created_at=tenant.created_at,
            updated_at=tenant.updated_at
        )
    
    async def get_tenants(self, skip: int = 0, limit: int = 20) -> List[TenantResponse]:
        """Get list of tenants with pagination"""
        tenants = self.db.query(Tenant).offset(skip).limit(limit).all()
        
        return [
            TenantResponse(
                id=tenant.id,
                name=tenant.name,
                package=tenant.package,
                expired_date=tenant.expired_date,
                cycle=tenant.cycle,
                manager_id=tenant.manager_id,
                phone=tenant.phone,
                created_by=tenant.created_by,
                created_at=tenant.created_at,
                updated_at=tenant.updated_at
            ) for tenant in tenants
        ]
    
    async def get_total_tenants_count(self) -> int:
        """Get total count of tenants"""
        return self.db.query(Tenant).count()
    
    async def update_tenant(self, tenant_id: str, tenant_data: TenantUpdate) -> TenantResponse:
        """Update tenant information"""
        tenant = self.db.query(Tenant).filter(Tenant.id == tenant_id).first()
        if not tenant:
            raise APIException(
                error_code="TENANT_001",
                message="Tenant not found"
            )
        
        # Check if manager exists (if manager_id is being updated)
        if tenant_data.manager_id:
            manager = self.db.query(User).filter(User.id == tenant_data.manager_id).first()
            if not manager:
                raise APIException(
                    error_code=ErrorCodes.USER_NOT_FOUND,
                    message="Manager not found"
                )
        
        # Update tenant fields
        update_data = tenant_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(tenant, field, value)
        
        self.db.commit()
        self.db.refresh(tenant)
        
        return TenantResponse(
            id=tenant.id,
            name=tenant.name,
            package=tenant.package,
            expired_date=tenant.expired_date,
            cycle=tenant.cycle,
            manager_id=tenant.manager_id,
            phone=tenant.phone,
            created_by=tenant.created_by,
            created_at=tenant.created_at,
            updated_at=tenant.updated_at
        )
    
    async def delete_tenant(self, tenant_id: str) -> dict:
        """Delete tenant"""
        tenant = self.db.query(Tenant).filter(Tenant.id == tenant_id).first()
        if not tenant:
            raise APIException(
                error_code="TENANT_001",
                message="Tenant not found"
            )
        
        # Check if tenant has associated users
        users_count = self.db.query(User).filter(User.tenant_id == tenant_id).count()
        if users_count > 0:
            raise APIException(
                error_code="TENANT_002",
                message="Cannot delete tenant with associated users"
            )
        
        self.db.delete(tenant)
        self.db.commit()
        
        return {"message": "Tenant successfully deleted"}


def get_tenant_service(db: Session = Depends(get_db)) -> TenantService:
    """Dependency to get TenantService instance"""
    return TenantService(db)