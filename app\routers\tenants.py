from fastapi import APIRouter, Depends, Query
from typing import List
from ..schemas.tenant import TenantCreate, TenantUpdate, TenantResponse, TenantStandardResponse, TenantListStandardResponse, DeleteStandardResponse
from ..services.tenant_service import get_tenant_service, TenantService
from ..middleware.auth import get_current_user, require_super_admin
from ..utils.responses import success_response, paginated_response

router = APIRouter(prefix="/tenants", tags=["tenants"])

@router.get("/", response_model=TenantListStandardResponse)
async def get_tenants(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    current_user: dict = require_super_admin(),
    tenant_service: TenantService = Depends(get_tenant_service)
):
    """Get list of tenants with pagination"""
    skip = (page - 1) * size
    tenants = await tenant_service.get_tenants(skip=skip, limit=size)
    total = await tenant_service.get_total_tenants_count()
    
    return paginated_response(
        data=tenants,
        total=total,
        page=page,
        size=size,
        message="Tenants retrieved successfully"
    )

@router.get("/{tenant_id}", response_model=TenantStandardResponse)
async def get_tenant(
    tenant_id: str,
    current_user: dict = Depends(get_current_user),
    tenant_service: TenantService = Depends(get_tenant_service)
):
    """Get tenant by ID"""
    tenant = await tenant_service.get_tenant_by_id(tenant_id)
    return success_response(data=tenant, message="Tenant retrieved successfully")

@router.post("/", response_model=TenantStandardResponse)
async def create_tenant(
    tenant_data: TenantCreate,
    current_user: dict = Depends(get_current_user),
    tenant_service: TenantService = Depends(get_tenant_service)
):
    """Create a new tenant"""
    tenant = await tenant_service.create_tenant(tenant_data)
    return success_response(data=tenant, message="Tenant created successfully")

@router.put("/{tenant_id}", response_model=TenantStandardResponse)
async def update_tenant(
    tenant_id: str,
    tenant_data: TenantUpdate,
    current_user: dict = Depends(get_current_user),
    tenant_service: TenantService = Depends(get_tenant_service)
):
    """Update tenant information"""
    tenant = await tenant_service.update_tenant(tenant_id, tenant_data)
    return success_response(data=tenant, message="Tenant updated successfully")

@router.delete("/{tenant_id}", response_model=DeleteStandardResponse)
async def delete_tenant(
    tenant_id: str,
    current_user: dict = Depends(get_current_user),
    tenant_service: TenantService = Depends(get_tenant_service)
):
    """Delete tenant"""
    result = await tenant_service.delete_tenant(tenant_id)
    return success_response(data=result, message="Tenant deleted successfully")