from fastapi import HTTPException, Request
from fastapi.responses import JSONResponse
from typing import Any, Dict, Optional, TypeVar, Union
from datetime import datetime
from ..schemas.responses import StandardResponse, PaginatedResponse, ErrorDetail

T = TypeVar('T')

def success_response(data: T, message: Optional[str] = None) -> StandardResponse[T]:
    """Create a standardized success response"""
    return StandardResponse.success_response(data=data, message=message)

def error_response(code: str, message: str, details: Optional[Dict[str, Any]] = None) -> StandardResponse[None]:
    """Create a standardized error response"""
    return StandardResponse.error_response(code=code, message=message, details=details)

def paginated_response(
    data: list[T], 
    total: int, 
    page: int, 
    size: int, 
    message: Optional[str] = None
) -> PaginatedResponse[T]:
    """Create a standardized paginated response"""
    return PaginatedResponse.create(data=data, total=total, page=page, size=size, message=message)

class APIException(HTTPException):
    """Custom API Exception with error code support"""
    def __init__(
        self, 
        status_code: int, 
        error_code: str, 
        message: str, 
        details: Optional[Dict[str, Any]] = None
    ):
        self.error_code = error_code
        self.message = message
        self.details = details
        super().__init__(status_code=status_code, detail=message)

# Error code constants
class ErrorCodes:
    # Authentication errors
    AUTH_INVALID_CREDENTIALS = "AUTH_001"
    AUTH_TOKEN_EXPIRED = "AUTH_002"
    AUTH_INSUFFICIENT_PERMISSIONS = "AUTH_003"
    AUTH_TOKEN_INVALID = "AUTH_004"
    
    # User errors
    USER_NOT_FOUND = "USER_001"
    USER_ALREADY_EXISTS = "USER_002"
    USER_INVALID_DATA = "USER_003"
    
    # Permission errors
    PERM_NOT_FOUND = "PERM_001"
    PERM_ALREADY_ASSIGNED = "PERM_002"
    PERM_ACCESS_DENIED = "PERM_003"
    
    # Role errors
    ROLE_NOT_FOUND = "ROLE_001"
    ROLE_ALREADY_EXISTS = "ROLE_002"
    
    # General errors
    VALIDATION_ERROR = "VAL_001"
    SERVER_ERROR = "SRV_001"
    NOT_FOUND = "NOT_001"
    BAD_REQUEST = "BAD_001"

async def api_exception_handler(request: Request, exc: APIException) -> JSONResponse:
    """Handle custom API exceptions and return standardized error response"""
    error_response_data = StandardResponse.error_response(
        code=exc.error_code,
        message=exc.message,
        details=exc.details
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response_data.model_dump()
    )

async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """Handle standard HTTP exceptions and return standardized error response"""
    # Map status codes to error codes
    error_code_mapping = {
        400: ErrorCodes.BAD_REQUEST,
        401: ErrorCodes.AUTH_INVALID_CREDENTIALS,
        403: ErrorCodes.AUTH_INSUFFICIENT_PERMISSIONS,
        404: ErrorCodes.NOT_FOUND,
        500: ErrorCodes.SERVER_ERROR
    }
    
    error_code = error_code_mapping.get(exc.status_code, ErrorCodes.SERVER_ERROR)
    
    error_response_data = {
        "success": False,
        "error": {
            "code": error_code,
            "message": str(exc.detail),
            "details": None
        },
        "timestamp": datetime.utcnow().isoformat(),
        "path": str(request.url.path)
    }
    
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response_data
    )

async def validation_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """Handle validation exceptions and return standardized error response"""
    error_response_data = {
        "success": False,
        "error": {
            "code": ErrorCodes.VALIDATION_ERROR,
            "message": "Validation error",
            "details": {"validation_errors": str(exc)}
        },
        "timestamp": datetime.utcnow().isoformat(),
        "path": str(request.url.path)
    }
    
    return JSONResponse(
        status_code=422,
        content=error_response_data
    )