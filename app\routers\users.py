from fastapi import APIRouter, Depends, HTTPException, Query
from typing import List
from ..schemas.user import UserCreate, UserUpdate, UserResponse, UserStandardResponse, UserListStandardResponse, DeleteStandardResponse
from ..services.user_service import get_user_service, UserService
from ..middleware.auth import get_current_user, get_current_user_with_role
from ..utils.responses import success_response, paginated_response, APIException, ErrorCodes

router = APIRouter(prefix="/users", tags=["users"])

@router.get("/", response_model=UserListStandardResponse)
async def get_users(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    current_user: dict = Depends(get_current_user_with_role),
    user_service: UserService = Depends(get_user_service)
):
    """Get list of users with proper tenant filtering based on user role"""
    skip = (page - 1) * size
    
    # Super admins see all users, others see only their tenant's users
    users = await user_service.get_users(
        skip=skip, 
        limit=size,
        tenant_id=current_user["tenant_id"],
        is_super_admin=current_user["is_super_admin"]
    )
    total = await user_service.get_total_users_count(
        tenant_id=current_user["tenant_id"],
        is_super_admin=current_user["is_super_admin"]
    )
    
    return paginated_response(
        data=users,
        total=total,
        page=page,
        size=size,
        message="Users retrieved successfully"
    )

@router.get("/{user_id}", response_model=UserStandardResponse)
async def get_user(
    user_id: str,
    current_user: dict = Depends(get_current_user),
    user_service: UserService = Depends(get_user_service)
):
    user = await user_service.get_user_by_id(user_id)
    return success_response(data=user, message="User retrieved successfully")

@router.get("/by-keycloak-id/{keycloak_id}", response_model=UserStandardResponse)
async def get_user_by_keycloak_id(
    keycloak_id: str,
    current_user: dict = Depends(get_current_user),
    user_service: UserService = Depends(get_user_service)
):
    user = await user_service.get_user_by_keycloak_id(keycloak_id)
    return success_response(data=user, message="User retrieved successfully")

@router.post("/", response_model=UserStandardResponse)
async def create_user(
    user_data: UserCreate,
    current_user: dict = Depends(get_current_user),
    user_service: UserService = Depends(get_user_service)
):
    user = await user_service.create_user(user_data)
    return success_response(data=user, message="User created successfully")

@router.put("/{user_id}", response_model=UserStandardResponse)
async def update_user(
    user_id: str,
    user_data: UserUpdate,
    current_user: dict = Depends(get_current_user),
    user_service: UserService = Depends(get_user_service)
):
    user = await user_service.update_user(user_id, user_data)
    return success_response(data=user, message="User updated successfully")

@router.delete("/{user_id}", response_model=DeleteStandardResponse)
async def delete_user(
    user_id: str,
    current_user: dict = Depends(get_current_user),
    user_service: UserService = Depends(get_user_service)
):
    await user_service.delete_user(user_id)
    return success_response(
        data={"deleted": True}, 
        message="User deleted successfully"
    )