#!/usr/bin/env python3

"""
Quick script to check user roles and tenant_id assignment
"""

import os
import sys

# Add app to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.database.session import get_db
from app.models.user import User
from app.models.role import Role
from sqlalchemy.orm import Session

def check_users():
    """Check current users and their roles"""
    db = next(get_db())
    
    try:
        # Get all users with their roles
        users = db.query(User).join(Role, User.role_id == Role.id, isouter=True).all()
        
        print("Current Users in Database:")
        print("-" * 80)
        
        for user in users:
            role_name = user.role.name if user.role else "No Role"
            role_code = user.role.code if user.role else "No Code"
            tenant_status = "NULL (Super Admin)" if user.tenant_id is None else f"Tenant: {user.tenant_id}"
            
            print(f"USER: {user.username}")
            print(f"   Email: {user.email}")
            print(f"   Role: {role_name} ({role_code})")
            print(f"   Tenant: {tenant_status}")
            print(f"   User ID: {user.id}")
            print(f"   Keycloak ID: {user.keycloak_id}")
            print()
            
        # Check if we have any super admins
        super_admins = db.query(User).join(Role).filter(Role.code == "SUPER_ADMIN").all()
        print(f"Super Admins: {len(super_admins)}")
        
        # Check roles
        print("\nAvailable Roles:")
        print("-" * 40)
        roles = db.query(Role).all()
        for role in roles:
            print(f"ROLE: {role.name} ({role.code}) - ID: {role.id}")
            
    finally:
        db.close()

if __name__ == "__main__":
    check_users()