# Standardized API Response Format

This document describes the standardized response format implemented across all FastAPI endpoints in the Clover ERP system.

## Response Structure

All API responses follow a consistent structure that includes success/error status, data payload, optional messages, and timestamps.

### Success Response Format

```json
{
  "success": true,
  "data": <response_data>,
  "message": "Optional success message",
  "timestamp": "2024-01-01T12:00:00.000000"
}
```

### Error Response Format

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human-readable error message",
    "details": {
      "additional": "context information"
    }
  },
  "timestamp": "2024-01-01T12:00:00.000000",
  "path": "/api/endpoint"
}
```

### Paginated Response Format

```json
{
  "success": true,
  "data": [<array_of_items>],
  "total": 100,
  "page": 1,
  "size": 20,
  "total_pages": 5,
  "message": "Optional message",
  "timestamp": "2024-01-01T12:00:00.000000"
}
```

## Error Codes

### Authentication Errors
- `AUTH_001`: Invalid credentials
- `AUTH_002`: Token expired
- `AUTH_003`: Insufficient permissions
- `AUTH_004`: Invalid token

### User Errors
- `USER_001`: User not found
- `USER_002`: User already exists
- `USER_003`: Invalid user data

### Permission Errors
- `PERM_001`: Permission not found
- `PERM_002`: Permission already assigned
- `PERM_003`: Access denied

### Role Errors
- `ROLE_001`: Role not found
- `ROLE_002`: Role already exists

### General Errors
- `VAL_001`: Validation error
- `SRV_001`: Server error
- `NOT_001`: Resource not found
- `BAD_001`: Bad request

## Example API Responses

### 1. User Login (Success)

**Request:** `POST /api/auth/login`
```json
{
  "username": "john.doe",
  "password": "password123"
}
```

**Response:** `200 OK`
```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJSUzI1NiIs...",
    "refresh_token": "eyJhbGciOiJSUzI1NiIs...",
    "token_type": "bearer",
    "expires_in": 3600
  },
  "message": "Login successful",
  "timestamp": "2024-01-01T12:00:00.000000"
}
```

### 2. User Login (Error)

**Request:** `POST /api/auth/login`
```json
{
  "username": "john.doe",
  "password": "wrongpassword"
}
```

**Response:** `401 Unauthorized`
```json
{
  "success": false,
  "error": {
    "code": "AUTH_001",
    "message": "Invalid credentials",
    "details": {
      "keycloak_error": "Invalid user credentials"
    }
  },
  "timestamp": "2024-01-01T12:00:00.000000",
  "path": "/api/auth/login"
}
```

### 3. Get Users (Paginated Success)

**Request:** `GET /api/users?page=1&size=2`

**Response:** `200 OK`
```json
{
  "success": true,
  "data": [
    {
      "id": "user-123",
      "full_name": "John Doe",
      "email": "<EMAIL>",
      "username": "john.doe",
      "role_id": "role-456",
      "keycloak_id": "keycloak-789",
      "created_at": "2024-01-01T10:00:00.000000",
      "updated_at": "2024-01-01T10:00:00.000000"
    },
    {
      "id": "user-124",
      "full_name": "Jane Smith",
      "email": "<EMAIL>",
      "username": "jane.smith",
      "role_id": "role-457",
      "keycloak_id": "keycloak-790",
      "created_at": "2024-01-01T11:00:00.000000",
      "updated_at": "2024-01-01T11:00:00.000000"
    }
  ],
  "total": 50,
  "page": 1,
  "size": 2,
  "total_pages": 25,
  "message": "Users retrieved successfully",
  "timestamp": "2024-01-01T12:00:00.000000"
}
```

### 4. Get Single User (Success)

**Request:** `GET /api/users/user-123`

**Response:** `200 OK`
```json
{
  "success": true,
  "data": {
    "id": "user-123",
    "full_name": "John Doe",
    "email": "<EMAIL>",
    "username": "john.doe",
    "role_id": "role-456",
    "keycloak_id": "keycloak-789",
    "created_at": "2024-01-01T10:00:00.000000",
    "updated_at": "2024-01-01T10:00:00.000000"
  },
  "message": "User retrieved successfully",
  "timestamp": "2024-01-01T12:00:00.000000"
}
```

### 5. Get Single User (Not Found)

**Request:** `GET /api/users/nonexistent-id`

**Response:** `404 Not Found`
```json
{
  "success": false,
  "error": {
    "code": "USER_001",
    "message": "User not found",
    "details": {
      "user_id": "nonexistent-id"
    }
  },
  "timestamp": "2024-01-01T12:00:00.000000",
  "path": "/api/users/nonexistent-id"
}
```

### 6. Create User (Success)

**Request:** `POST /api/users`
```json
{
  "full_name": "Alice Johnson",
  "email": "<EMAIL>",
  "username": "alice.johnson",
  "password": "securepassword",
  "role_id": "role-456"
}
```

**Response:** `200 OK`
```json
{
  "success": true,
  "data": {
    "id": "user-125",
    "full_name": "Alice Johnson",
    "email": "<EMAIL>",
    "username": "alice.johnson",
    "role_id": "role-456",
    "keycloak_id": "keycloak-791",
    "created_at": "2024-01-01T12:00:00.000000",
    "updated_at": "2024-01-01T12:00:00.000000"
  },
  "message": "User created successfully",
  "timestamp": "2024-01-01T12:00:00.000000"
}
```

### 7. Create User (Validation Error)

**Request:** `POST /api/users`
```json
{
  "full_name": "Bob Wilson",
  "email": "invalid-email",
  "username": "bob.wilson",
  "password": "password",
  "role_id": "role-456"
}
```

**Response:** `422 Unprocessable Entity`
```json
{
  "success": false,
  "error": {
    "code": "VAL_001",
    "message": "Validation error",
    "details": {
      "validation_errors": [
        {
          "loc": ["body", "email"],
          "msg": "field required",
          "type": "value_error.email"
        }
      ]
    }
  },
  "timestamp": "2024-01-01T12:00:00.000000",
  "path": "/api/users"
}
```

### 8. Delete User (Success)

**Request:** `DELETE /api/users/user-123`

**Response:** `200 OK`
```json
{
  "success": true,
  "data": {
    "deleted": true
  },
  "message": "User deleted successfully",
  "timestamp": "2024-01-01T12:00:00.000000"
}
```

### 9. Token Refresh (Success)

**Request:** `POST /api/auth/refresh`
```json
{
  "refresh_token": "eyJhbGciOiJSUzI1NiIs..."
}
```

**Response:** `200 OK`
```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJSUzI1NiIs...",
    "refresh_token": "eyJhbGciOiJSUzI1NiIs...",
    "token_type": "bearer",
    "expires_in": 3600
  },
  "message": "Token refreshed successfully",
  "timestamp": "2024-01-01T12:00:00.000000"
}
```

### 10. Logout (Success)

**Request:** `POST /api/auth/logout`
**Headers:** `Authorization: Bearer eyJhbGciOiJSUzI1NiIs...`

**Response:** `200 OK`
```json
{
  "success": true,
  "data": {
    "logged_out": true
  },
  "message": "Logout successful",
  "timestamp": "2024-01-01T12:00:00.000000"
}
```

## Implementation Usage

### Using Response Utilities

The system provides utility functions to create standardized responses:

```python
from app.utils.responses import success_response, error_response, paginated_response, APIException, ErrorCodes

# Success response
return success_response(
    data=user_data,
    message="User retrieved successfully"
)

# Paginated response
return paginated_response(
    data=users,
    total=total_count,
    page=page,
    size=size,
    message="Users retrieved successfully"
)

# Raise custom exception
raise APIException(
    status_code=404,
    error_code=ErrorCodes.USER_NOT_FOUND,
    message="User not found",
    details={"user_id": user_id}
)
```

### Response Models

All endpoints use standardized response models:

```python
from app.schemas.responses import StandardResponse, PaginatedResponse

@router.get("/users/{user_id}", response_model=StandardResponse[UserResponse])
async def get_user(user_id: str):
    # ... endpoint logic
    return success_response(data=user, message="User retrieved successfully")

@router.get("/users", response_model=PaginatedResponse[UserResponse])
async def get_users(page: int = 1, size: int = 20):
    # ... endpoint logic
    return paginated_response(data=users, total=total, page=page, size=size)
```

## Benefits

1. **Consistency**: All API responses follow the same structure
2. **Error Handling**: Standardized error codes and messages
3. **Client-Friendly**: Predictable response format for frontend consumption
4. **Debugging**: Detailed error information with timestamps and paths
5. **Type Safety**: Pydantic models ensure response structure validity
6. **Maintainability**: Centralized response handling logic

## Migration Notes

When updating existing endpoints:

1. Import response utilities and models
2. Update response_model in route decorators
3. Replace direct return statements with success_response() calls
4. Replace HTTPException with APIException for better error handling
5. Test all endpoints to ensure proper response formatting

This standardization ensures a consistent and professional API experience across all endpoints.