version: '3.8'

services:
  fastapi:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=************************************************/clover_erp
      - KEY<PERSON>OAK_SERVER_URL=http://keycloak:8080
      - K<PERSON><PERSON>CLOAK_REALM=clover-realm
      - KEYCLOAK_CLIENT_ID=fastapi-backend
      - <PERSON><PERSON><PERSON>CLOAK_CLIENT_SECRET=your-client-secret
      - K<PERSON><PERSON>CLOAK_ADMIN_USERNAME=admin
      - KEYCLOAK_ADMIN_PASSWORD=admin
    depends_on:
      - keycloak
      - db
    volumes:
      - .:/app
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  keycloak:
    image: quay.io/keycloak/keycloak:23.0
    environment:
      - KEYCLOAK_ADMIN=admin
      - KEYCLOAK_ADMIN_PASSWORD=admin
      - KC_DB=postgres
      - KC_DB_URL=*******************************************
      - KC_DB_USERNAME=keycloak_user
      - KC_DB_PASSWORD=keycloak_password
    ports:
      - "8080:8080"
    command: start-dev
    depends_on:
      - keycloak_db

  db:
    image: postgres:15
    environment:
      - POSTGRES_USER=clover_user
      - POSTGRES_PASSWORD=clover_password
      - POSTGRES_DB=clover_erp
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  keycloak_db:
    image: postgres:15
    environment:
      - POSTGRES_USER=keycloak_user
      - POSTGRES_PASSWORD=keycloak_password
      - POSTGRES_DB=keycloak
    volumes:
      - keycloak_postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
  keycloak_postgres_data: