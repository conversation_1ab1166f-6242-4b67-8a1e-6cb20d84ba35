from sqlalchemy import Column, String, ForeignKey
from sqlalchemy.orm import relationship
from .base import BaseModel


class School(BaseModel):
    __tablename__ = "schools"
    
    name = Column(String, nullable=False)
    code = Column(String, nullable=False, unique=True, index=True)
    address = Column(String, nullable=True)
    phone = Column(String, nullable=True)
    tenant_id = Column(String, ForeignKey("tenants.id"), nullable=False)
    manager_id = Column(String, ForeignKey("users.id"), nullable=False)
    
    # Relationships
    tenant = relationship("Tenant")
    manager = relationship("User", foreign_keys=[manager_id])