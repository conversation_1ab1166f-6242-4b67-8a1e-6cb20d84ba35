# CLAUDE.md - FastAPI Backend with Keycloak Integration

## Project Overview

This FastAPI backend integrates with Keycloak for comprehensive user authentication and authorization management. The system leverages Keycloak's robust identity and access management capabilities while providing a custom permission management layer with multi-tenant support.

## Standardized API Response Format

**CRITICAL**: All API endpoints MUST follow the standardized response format defined in `STANDARDIZED_RESPONSES.md`. This ensures consistency across all endpoints and provides a predictable structure for client applications.

### Response Structure Requirements

#### Success Response
```json
{
  "success": true,
  "data": <response_data>,
  "message": "Optional success message",
  "timestamp": "2024-01-01T12:00:00.000000"
}
```

#### Error Response
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human-readable error message",
    "details": {
      "additional": "context information"
    }
  },
  "timestamp": "2024-01-01T12:00:00.000000",
  "path": "/api/endpoint"
}
```

#### Paginated Response
```json
{
  "success": true,
  "data": [<array_of_items>],
  "total": 100,
  "page": 1,
  "size": 20,
  "total_pages": 5,
  "message": "Optional message",
  "timestamp": "2024-01-01T12:00:00.000000"
}
```

### Response Utilities Usage

**MANDATORY**: Use the provided response utility functions:

```python
from app.utils.responses import success_response, paginated_response, APIException, ErrorCodes

# For single item responses
return success_response(
    data=item,
    message="Item retrieved successfully"
)

# For paginated responses
return paginated_response(
    data=items,
    total=total_count,
    page=page,
    size=size,
    message="Items retrieved successfully"
)

# For errors
raise APIException(
    status_code=404,
    error_code=ErrorCodes.RESOURCE_NOT_FOUND,
    message="Resource not found"
)
```

## Architecture

### Core Components

1. **FastAPI Backend**: RESTful API server handling business logic
2. **Keycloak Server**: Identity and Access Management (IAM) provider
3. **Database**: Stores application-specific data (permissions, user-permission mappings)
4. **Keycloak Admin API**: Interface for user and role management

### Data Models

#### Multi-Tenant Architecture

The system implements multi-tenancy with a hierarchical permission structure:
- **Tenant-specific**: User, UserPermission, School (have tenant_id)
- **Shared across tenants**: Role, Permission (no tenant_id - universal concepts)

#### Hierarchical Permission System

The system supports three levels of administrative access:

1. **Super Admin**
   - Has access to ALL data across ALL tenants
   - Can manage all tenants, users, schools, and system-wide settings
   - Not restricted by tenant_id boundaries
   - Role: `super_admin`

2. **Tenant Admin** 
   - Has full access within their assigned tenant only
   - Can manage all users and schools within their tenant
   - Cannot access data from other tenants
   - Role: `tenant_admin`
   - Scope: Limited by `user.tenant_id`

3. **School Admin**
   - Has access only within their assigned school(s)
   - Can manage users and resources within their school
   - Cannot access other schools, even within the same tenant
   - Role: `school_admin`
   - Scope: Limited by `school.manager_id = user.id`

#### Data Access Patterns

```python
# Super Admin - No filtering
def get_data_super_admin():
    return db.query(Model).all()

# Tenant Admin - Filter by tenant_id
def get_data_tenant_admin(user_tenant_id: str):
    return db.query(Model).filter(Model.tenant_id == user_tenant_id).all()

# School Admin - Filter by managed schools
def get_data_school_admin(user_id: str):
    return db.query(Model).join(School).filter(School.manager_id == user_id).all()
```

#### Tenant Model
```python
class Tenant:
    id: str
    name: str
    package: PackageType  # TRIAL, PRO, PRO_MAX
    expired_date: datetime
    cycle: CycleType  # 7_DAYS, 1_MONTH, 6_MONTHS, 1_YEAR
    manager_id: str  # References User.id
    phone: str
    created_by: str
    created_at: datetime
    updated_at: datetime
```

#### User Model
```python
class User:
    id: str  # Local database ID
    keycloak_id: str  # Keycloak user ID
    full_name: str
    email: str
    username: str
    password: str  # Handled by Keycloak, not stored locally
    role_id: str
    tenant_id: str  # NEW: Links user to tenant
    created_at: datetime
    updated_at: datetime
```

#### Role Model (Shared across tenants)
```python
class Role:
    id: str
    name: str
    code: str
    created_at: datetime
    updated_at: datetime
```

#### Permission Model (Shared across tenants)
```python
class Permission:
    id: str
    name: str
    resource: str
    action: str
    description: str
    created_at: datetime
    updated_at: datetime
```

#### UserPermission Model
```python
class UserPermission:
    id: str
    user_id: str
    permission_id: str
    granted_at: datetime
    granted_by: str
    tenant_id: str  # NEW: Tenant-specific permission assignments
    created_at: datetime
    updated_at: datetime
```

#### School Model (Tenant-specific)
```python
class School:
    id: str  # Local database ID
    name: str
    code: str  # Unique within tenant
    address: str
    phone: str
    tenant_id: str  # Links school to tenant
    manager_id: str  # References User.id (School Admin)
    created_at: datetime
    updated_at: datetime
```

## Keycloak Configuration

### Initial Setup

1. **Realm Creation**
   - Create a new realm for your application
   - Configure realm settings (token lifespans, security policies)

2. **Client Configuration**
   ```json
   {
     "clientId": "fastapi-backend",
     "enabled": true,
     "clientAuthenticatorType": "client-secret",
     "redirectUris": ["http://localhost:8000/*"],
     "webOrigins": ["http://localhost:8000"],
     "protocol": "openid-connect",
     "standardFlowEnabled": true,
     "directAccessGrantsEnabled": true,
     "serviceAccountsEnabled": true
   }
   ```

3. **Role Configuration**
   - Create realm roles matching your application roles
   - Map roles to Keycloak groups if needed

### Environment Variables

```env
KEYCLOAK_SERVER_URL=http://localhost:8080
KEYCLOAK_REALM=your-realm
KEYCLOAK_CLIENT_ID=fastapi-backend
KEYCLOAK_CLIENT_SECRET=your-client-secret
KEYCLOAK_ADMIN_USERNAME=admin
KEYCLOAK_ADMIN_PASSWORD=admin-password
```

## API Endpoints

### Authentication Endpoints

#### POST /auth/login
Authenticate user and receive access token
```json
Request:
{
  "username": "string",
  "password": "string"
}

Response:
{
  "success": true,
  "data": {
    "access_token": "string",
    "refresh_token": "string",
    "token_type": "bearer",
    "expires_in": 3600
  },
  "message": "Login successful",
  "timestamp": "2024-01-01T12:00:00.000000"
}
```

#### POST /auth/refresh
Refresh access token
```json
Request:
{
  "refresh_token": "string"
}

Response:
{
  "success": true,
  "data": {
    "access_token": "string",
    "refresh_token": "string",
    "token_type": "bearer",
    "expires_in": 3600
  },
  "message": "Token refreshed successfully",
  "timestamp": "2024-01-01T12:00:00.000000"
}
```

#### POST /auth/logout
Logout user and revoke tokens
```json
Request:
Headers: Authorization: Bearer <access_token>

Response:
{
  "message": "Successfully logged out"
}
```

### User Management Endpoints

#### GET /users
List all users (requires admin permission)
```json
Response:
{
  "users": [
    {
      "id": "string",
      "full_name": "string",
      "email": "string",
      "username": "string",
      "role_id": "string"
    }
  ],
  "total": 100,
  "page": 1,
  "size": 20
}
```

#### GET /users/{user_id}
Get specific user details
```json
Response:
{
  "id": "string",
  "full_name": "string",
  "email": "string",
  "username": "string",
  "role_id": "string",
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

#### POST /users
Create new user
```json
Request:
{
  "full_name": "string",
  "email": "string",
  "username": "string",
  "password": "string",
  "role_id": "string"
}

Response:
{
  "id": "string",
  "full_name": "string",
  "email": "string",
  "username": "string",
  "role_id": "string"
}
```

#### PUT /users/{user_id}
Update user information
```json
Request:
{
  "full_name": "string",
  "email": "string",
  "role_id": "string"
}

Response:
{
  "id": "string",
  "full_name": "string",
  "email": "string",
  "username": "string",
  "role_id": "string"
}
```

#### DELETE /users/{user_id}
Delete user
```json
Response:
{
  "message": "User successfully deleted"
}
```

### Tenant Management Endpoints

#### GET /tenants
List all tenants with pagination
```json
Request: GET /api/tenants?page=1&size=20

Response:
{
  "success": true,
  "data": [
    {
      "id": "string",
      "name": "string",
      "package": "pro",
      "expired_date": "2024-12-31T23:59:59Z",
      "cycle": "1_year",
      "manager_id": "string",
      "phone": "string",
      "created_by": "string",
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  ],
  "total": 50,
  "page": 1,
  "size": 20,
  "total_pages": 3,
  "message": "Tenants retrieved successfully",
  "timestamp": "2024-01-01T12:00:00.000000"
}
```

#### GET /tenants/{tenant_id}
Get specific tenant details
```json
Response:
{
  "success": true,
  "data": {
    "id": "string",
    "name": "string",
    "package": "pro_max",
    "expired_date": "2024-12-31T23:59:59Z",
    "cycle": "6_months",
    "manager_id": "string",
    "phone": "string",
    "created_by": "string",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  },
  "message": "Tenant retrieved successfully",
  "timestamp": "2024-01-01T12:00:00.000000"
}
```

#### POST /tenants
Create new tenant
```json
Request:
{
  "name": "Acme Corporation",
  "package": "pro",
  "expired_date": "2024-12-31T23:59:59Z",
  "cycle": "1_year",
  "manager_id": "string",
  "phone": "+1234567890",
  "created_by": "string"
}

Response:
{
  "success": true,
  "data": {
    "id": "string",
    "name": "Acme Corporation",
    "package": "pro",
    "expired_date": "2024-12-31T23:59:59Z",
    "cycle": "1_year",
    "manager_id": "string",
    "phone": "+1234567890",
    "created_by": "string",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  },
  "message": "Tenant created successfully",
  "timestamp": "2024-01-01T12:00:00.000000"
}
```

#### PUT /tenants/{tenant_id}
Update tenant information
```json
Request:
{
  "name": "Updated Corp Name",
  "package": "pro_max",
  "phone": "+0987654321"
}

Response:
{
  "success": true,
  "data": {
    "id": "string",
    "name": "Updated Corp Name",
    "package": "pro_max",
    "expired_date": "2024-12-31T23:59:59Z",
    "cycle": "1_year",
    "manager_id": "string",
    "phone": "+0987654321",
    "created_by": "string",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T10:00:00Z"
  },
  "message": "Tenant updated successfully",
  "timestamp": "2024-01-01T12:00:00.000000"
}
```

#### DELETE /tenants/{tenant_id}
Delete tenant (only if no associated users)
```json
Response:
{
  "success": true,
  "data": {
    "message": "Tenant successfully deleted"
  },
  "message": "Tenant deleted successfully",
  "timestamp": "2024-01-01T12:00:00.000000"
}
```

### School Management Endpoints

#### GET /schools
List schools with automatic tenant filtering based on user role
```json
Request: GET /api/schools?page=1&size=20

Response (Tenant Admin or School Admin):
{
  "success": true,
  "data": [
    {
      "id": "string",
      "name": "string",
      "code": "string",
      "address": "string",
      "phone": "string",
      "tenant_id": "string",
      "manager_id": "string",
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  ],
  "total": 10,
  "page": 1,
  "size": 20,
  "total_pages": 1,
  "message": "Schools retrieved successfully",
  "timestamp": "2024-01-01T12:00:00.000000"
}
```

#### GET /schools/{school_id}
Get specific school details (filtered by user permissions)
```json
Response:
{
  "success": true,
  "data": {
    "id": "string",
    "name": "string",
    "code": "string",
    "address": "string",
    "phone": "string",
    "tenant_id": "string",
    "manager_id": "string",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  },
  "message": "School retrieved successfully",
  "timestamp": "2024-01-01T12:00:00.000000"
}
```

#### POST /schools
Create new school (automatically assigned to current user's tenant)
```json
Request:
{
  "name": "Lincoln High School",
  "code": "LHS001",
  "address": "123 Education St, City",
  "phone": "+1234567890",
  "manager_id": "user-id-of-school-admin"
}

Response:
{
  "success": true,
  "data": {
    "id": "string",
    "name": "Lincoln High School",
    "code": "LHS001",
    "address": "123 Education St, City",
    "phone": "+1234567890",
    "tenant_id": "auto-assigned-from-current-user",
    "manager_id": "user-id-of-school-admin",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  },
  "message": "School created successfully",
  "timestamp": "2024-01-01T12:00:00.000000"
}
```

#### PUT /schools/{school_id}
Update school information (filtered by user permissions)
```json
Request:
{
  "name": "Updated School Name",
  "address": "456 New Address",
  "phone": "+0987654321",
  "manager_id": "new-manager-id"
}

Response:
{
  "success": true,
  "data": {
    "id": "string",
    "name": "Updated School Name",
    "code": "LHS001",
    "address": "456 New Address",
    "phone": "+0987654321",
    "tenant_id": "string",
    "manager_id": "new-manager-id",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T10:00:00Z"
  },
  "message": "School updated successfully",
  "timestamp": "2024-01-01T12:00:00.000000"
}
```

#### DELETE /schools/{school_id}
Delete school (filtered by user permissions)
```json
Response:
{
  "success": true,
  "data": {
    "message": "School successfully deleted"
  },
  "message": "School deleted successfully",
  "timestamp": "2024-01-01T12:00:00.000000"
}
```

### Permission Management Endpoints

#### GET /permissions
List all available permissions
```json
Response:
{
  "permissions": [
    {
      "id": "string",
      "name": "string",
      "resource": "string",
      "action": "string",
      "description": "string"
    }
  ]
}
```

#### POST /permissions
Create new permission
```json
Request:
{
  "name": "string",
  "resource": "string",
  "action": "string",
  "description": "string"
}

Response:
{
  "id": "string",
  "name": "string",
  "resource": "string",
  "action": "string",
  "description": "string"
}
```

#### GET /users/{user_id}/permissions
Get user's permissions
```json
Response:
{
  "user_id": "string",
  "permissions": [
    {
      "id": "string",
      "name": "string",
      "resource": "string",
      "action": "string",
      "granted_at": "2024-01-01T00:00:00Z"
    }
  ]
}
```

#### POST /users/{user_id}/permissions
Assign permission to user
```json
Request:
{
  "permission_id": "string"
}

Response:
{
  "message": "Permission successfully assigned",
  "user_permission_id": "string"
}
```

#### DELETE /users/{user_id}/permissions/{permission_id}
Revoke permission from user
```json
Response:
{
  "message": "Permission successfully revoked"
}
```

### Role Management Endpoints

#### GET /roles
List all roles
```json
Response:
{
  "roles": [
    {
      "id": "string",
      "name": "string",
      "code": "string"
    }
  ]
}
```

#### POST /roles
Create new role
```json
Request:
{
  "name": "string",
  "code": "string"
}

Response:
{
  "id": "string",
  "name": "string",
  "code": "string"
}
```

## CRITICAL Implementation Guidelines

### Standardized Response Format Compliance

**MANDATORY REQUIREMENT**: All new API endpoints and modifications to existing endpoints MUST follow the standardized response format defined in `STANDARDIZED_RESPONSES.md`. 

#### Before Creating Any Endpoint:
1. Import response utilities: `from app.utils.responses import success_response, paginated_response, APIException, ErrorCodes`
2. Use proper response models: `StandardResponse[DataType]` or `PaginatedResponse[DataType]`
3. Return responses using utility functions (never return raw dictionaries)
4. Handle errors using `APIException` with proper error codes

#### Code Review Checklist:
- ✅ All responses include `success`, `data`, `message`, `timestamp` fields
- ✅ Error responses include `error.code`, `error.message`, `error.details` fields  
- ✅ Paginated responses include `total`, `page`, `size`, `total_pages` fields
- ✅ No raw dictionaries or HTTPExceptions returned directly
- ✅ Proper error codes from ErrorCodes enum are used

**Non-compliance with standardized responses will result in code rejection.**

## Implementation Guidelines

### Keycloak Integration Service

```python
from keycloak import KeycloakAdmin, KeycloakOpenID
from fastapi import HTTPException
from typing import Dict, List, Optional

class KeycloakService:
    def __init__(self):
        self.keycloak_openid = KeycloakOpenID(
            server_url=KEYCLOAK_SERVER_URL,
            client_id=KEYCLOAK_CLIENT_ID,
            realm_name=KEYCLOAK_REALM,
            client_secret_key=KEYCLOAK_CLIENT_SECRET
        )
        
        self.keycloak_admin = KeycloakAdmin(
            server_url=KEYCLOAK_SERVER_URL,
            username=KEYCLOAK_ADMIN_USERNAME,
            password=KEYCLOAK_ADMIN_PASSWORD,
            realm_name=KEYCLOAK_REALM,
            verify=True
        )
    
    async def create_user(self, user_data: Dict) -> str:
        """Create user in Keycloak"""
        try:
            user_id = self.keycloak_admin.create_user({
                "email": user_data["email"],
                "username": user_data["username"],
                "enabled": True,
                "firstName": user_data.get("first_name"),
                "lastName": user_data.get("last_name"),
                "credentials": [{
                    "type": "password",
                    "value": user_data["password"],
                    "temporary": False
                }]
            })
            return user_id
        except Exception as e:
            raise HTTPException(status_code=400, detail=str(e))
    
    async def authenticate_user(self, username: str, password: str) -> Dict:
        """Authenticate user and return tokens"""
        try:
            token = self.keycloak_openid.token(username, password)
            return token
        except Exception as e:
            raise HTTPException(status_code=401, detail="Invalid credentials")
    
    async def verify_token(self, token: str) -> Dict:
        """Verify and decode JWT token"""
        try:
            token_info = self.keycloak_openid.introspect(token)
            if not token_info.get("active"):
                raise HTTPException(status_code=401, detail="Token is not active")
            return token_info
        except Exception as e:
            raise HTTPException(status_code=401, detail="Invalid token")
```

### Permission Middleware

```python
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials

security = HTTPBearer()

class PermissionChecker:
    def __init__(self, required_permissions: List[str]):
        self.required_permissions = required_permissions
    
    async def __call__(
        self,
        credentials: HTTPAuthorizationCredentials = Depends(security),
        keycloak_service: KeycloakService = Depends(get_keycloak_service)
    ):
        token = credentials.credentials
        
        # Verify token
        token_info = await keycloak_service.verify_token(token)
        user_id = token_info.get("sub")
        
        # Check permissions
        user_permissions = await get_user_permissions(user_id)
        user_permission_codes = [p.code for p in user_permissions]
        
        for required in self.required_permissions:
            if required not in user_permission_codes:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Missing required permission: {required}"
                )
        
        return token_info
```

### Hierarchical Permission Middleware

```python
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from typing import List, Optional

security = HTTPBearer()

class HierarchicalPermissionChecker:
    """Enhanced permission checker supporting hierarchical access control"""
    
    @staticmethod
    async def get_current_user_with_role(
        credentials: HTTPAuthorizationCredentials = Depends(security),
        db: Session = Depends(get_db)
    ) -> dict:
        """Get current user with role and tenant information"""
        token = credentials.credentials
        token_info = await keycloak_service.verify_token(token)
        keycloak_user_id = token_info.get("sub")
        
        # Get user from local database
        user = db.query(User).filter(User.keycloak_id == keycloak_user_id).first()
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # Get user role
        role = db.query(Role).filter(Role.id == user.role_id).first()
        
        # Enhanced token info with role and tenant
        token_info.update({
            "local_user_id": user.id,
            "tenant_id": user.tenant_id,
            "role_code": role.code if role else None,
            "is_super_admin": role.code == "super_admin" if role else False,
            "is_tenant_admin": role.code == "tenant_admin" if role else False,
            "is_school_admin": role.code == "school_admin" if role else False
        })
        
        return token_info
    
    @staticmethod
    async def require_super_admin():
        """Middleware requiring super admin role"""
        def check_super_admin(current_user: dict = Depends(get_current_user_with_role)):
            if not current_user.get("is_super_admin"):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Super admin access required"
                )
            return current_user
        return Depends(check_super_admin)
    
    @staticmethod
    async def require_tenant_admin_or_above():
        """Middleware requiring tenant admin or super admin role"""
        def check_tenant_admin(current_user: dict = Depends(get_current_user_with_role)):
            if not (current_user.get("is_super_admin") or current_user.get("is_tenant_admin")):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Tenant admin or super admin access required"
                )
            return current_user
        return Depends(check_tenant_admin)
    
    @staticmethod
    async def require_school_admin_or_above():
        """Middleware requiring school admin, tenant admin, or super admin role"""
        def check_school_admin(current_user: dict = Depends(get_current_user_with_role)):
            if not (current_user.get("is_super_admin") or 
                   current_user.get("is_tenant_admin") or 
                   current_user.get("is_school_admin")):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Administrative access required"
                )
            return current_user
        return Depends(check_school_admin)

# Usage in endpoints
@router.get("/all-tenants")  # Super admin only
async def get_all_tenants(
    current_user: dict = Depends(HierarchicalPermissionChecker.require_super_admin)
):
    # Super admin can see all tenants
    pass

@router.get("/tenant-users")  # Tenant admin or super admin
async def get_tenant_users(
    current_user: dict = Depends(HierarchicalPermissionChecker.require_tenant_admin_or_above)
):
    # Filter by tenant unless super admin
    if current_user.get("is_super_admin"):
        # Super admin sees all users
        return get_all_users()
    else:
        # Tenant admin sees only their tenant's users
        return get_users_by_tenant(current_user["tenant_id"])

@router.get("/school-data")  # Any admin level
async def get_school_data(
    current_user: dict = Depends(HierarchicalPermissionChecker.require_school_admin_or_above)
):
    user_role = current_user.get("role_code")
    
    if current_user.get("is_super_admin"):
        # Super admin sees all schools
        return get_all_schools()
    elif current_user.get("is_tenant_admin"):
        # Tenant admin sees schools in their tenant
        return get_schools_by_tenant(current_user["tenant_id"])
    elif current_user.get("is_school_admin"):
        # School admin sees only schools they manage
        return get_schools_by_manager(current_user["local_user_id"])
```

### Database Schema (SQLAlchemy Example)

```python
from sqlalchemy import Column, String, DateTime, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship

Base = declarative_base()

class Role(Base):
    __tablename__ = "roles"
    
    id = Column(String, primary_key=True)
    name = Column(String, nullable=False, unique=True)
    code = Column(String, nullable=False, unique=True)
    created_at = Column(DateTime, default=datetime.utcnow)

class Permission(Base):
    __tablename__ = "permissions"
    
    id = Column(String, primary_key=True)
    name = Column(String, nullable=False)
    resource = Column(String, nullable=False)
    action = Column(String, nullable=False)
    description = Column(String)
    created_at = Column(DateTime, default=datetime.utcnow)

class UserPermission(Base):
    __tablename__ = "user_permissions"
    
    id = Column(String, primary_key=True)
    user_id = Column(String, nullable=False)  # Keycloak user ID
    permission_id = Column(String, ForeignKey("permissions.id"))
    granted_at = Column(DateTime, default=datetime.utcnow)
    granted_by = Column(String)
    
    permission = relationship("Permission")
```

## Security Best Practices

1. **Token Management**
   - Implement token refresh mechanism
   - Store refresh tokens securely
   - Set appropriate token expiration times

2. **Permission Validation**
   - Always validate permissions on the backend
   - Implement role-based access control (RBAC)
   - Use middleware for consistent permission checking

3. **Data Protection**
   - Never store passwords in your database
   - Use HTTPS in production
   - Implement rate limiting
   - Sanitize all user inputs

4. **Keycloak Security**
   - Enable brute force protection
   - Configure password policies
   - Enable two-factor authentication (2FA)
   - Regular security audits

## Error Handling

### Standard Error Response Format

```json
{
  "error": {
    "code": "ERROR_CODE",
    "message": "Human-readable error message",
    "details": {
      "field": "Additional error context"
    }
  },
  "timestamp": "2024-01-01T00:00:00Z",
  "path": "/api/endpoint"
}
```

### Common Error Codes

#### Authentication Errors
- `AUTH_001`: Invalid credentials
- `AUTH_002`: Token expired
- `AUTH_003`: Insufficient permissions
- `AUTH_004`: Invalid token

#### User Errors
- `USER_001`: User not found
- `USER_002`: User already exists
- `USER_003`: Invalid user data

#### Tenant Errors
- `TENANT_001`: Tenant not found
- `TENANT_002`: Cannot delete tenant with associated users
- `TENANT_003`: Tenant already exists
- `TENANT_004`: Invalid tenant data
- `TENANT_005`: Tenant subscription expired

#### School Errors
- `SCHOOL_001`: School not found
- `SCHOOL_002`: School code already exists
- `SCHOOL_003`: Cannot delete school with associated data
- `SCHOOL_004`: Invalid school data
- `SCHOOL_005`: School access denied (not in user's scope)

#### Permission Errors
- `PERM_001`: Permission not found
- `PERM_002`: Permission already assigned
- `PERM_003`: Access denied

#### Role Errors
- `ROLE_001`: Role not found
- `ROLE_002`: Role already exists

#### General Errors
- `VAL_001`: Validation error
- `SRV_001`: Server error
- `NOT_001`: Resource not found
- `BAD_001`: Bad request

## Testing Strategy

### Unit Tests
- Test individual service methods
- Mock Keycloak API calls
- Test permission validation logic

### Integration Tests
- Test complete API endpoints
- Test with real Keycloak instance (test environment)
- Test permission inheritance and cascading

### Load Testing
- Test concurrent user authentication
- Test permission checking performance
- Monitor Keycloak performance under load

## Deployment Considerations

### Docker Compose Configuration

```yaml
version: '3.8'

services:
  fastapi:
    build: .
    ports:
      - "8000:8000"
    environment:
      - KEYCLOAK_SERVER_URL=http://keycloak:8080
      - DATABASE_URL=**********************************/dbname
    depends_on:
      - keycloak
      - db
  
  keycloak:
    image: quay.io/keycloak/keycloak:latest
    environment:
      - KEYCLOAK_ADMIN=admin
      - KEYCLOAK_ADMIN_PASSWORD=admin
    ports:
      - "8080:8080"
    command: start-dev
  
  db:
    image: postgres:14
    environment:
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=dbname
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
```

### Production Checklist

- [ ] Configure Keycloak for production mode
- [ ] Set up SSL/TLS certificates
- [ ] Configure database connection pooling
- [ ] Implement logging and monitoring
- [ ] Set up backup strategies
- [ ] Configure rate limiting
- [ ] Implement caching where appropriate
- [ ] Set up CI/CD pipeline
- [ ] Document API with OpenAPI/Swagger

## Monitoring and Logging

### Key Metrics to Monitor

1. **Authentication Metrics**
   - Login success/failure rates
   - Token generation time
   - Active sessions count

2. **Permission Metrics**
   - Permission check frequency
   - Most accessed resources
   - Permission denial rates

3. **Performance Metrics**
   - API response times
   - Database query performance
   - Keycloak API latency

### Logging Strategy

```python
import logging
from fastapi import Request
import time

logger = logging.getLogger(__name__)

async def log_requests(request: Request, call_next):
    start_time = time.time()
    
    response = await call_next(request)
    
    process_time = time.time() - start_time
    logger.info(
        f"Path: {request.url.path} | "
        f"Method: {request.method} | "
        f"Status: {response.status_code} | "
        f"Duration: {process_time:.3f}s"
    )
    
    return response
```

## Troubleshooting Guide

### Common Issues and Solutions

1. **Token Validation Fails**
   - Check Keycloak server URL configuration
   - Verify realm and client settings
   - Ensure clock synchronization between servers

2. **User Creation Fails**
   - Verify admin credentials
   - Check Keycloak user attributes configuration
   - Ensure email uniqueness if required

3. **Permission Not Working**
   - Verify permission exists in database
   - Check user-permission mapping
   - Validate token contains correct user ID

## Additional Resources

- [Keycloak Documentation](https://www.keycloak.org/documentation)
- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [Python Keycloak Library](https://github.com/marcospereirampj/python-keycloak)
- [OAuth 2.0 Specification](https://oauth.net/2/)
- [OpenID Connect Specification](https://openid.net/connect/)

## Version History

- **v1.0.0** - Initial implementation with basic user and permission management
- **v1.1.0** - Added role-based access control
- **v1.2.0** - Implemented permission inheritance
- **v1.3.0** - Added audit logging and monitoring

---

*Last Updated: 2024*
*Maintained by: Development Team*