from fastapi import APIRouter, Depends
from typing import List
from ..schemas.permission import (
    PermissionCreate, PermissionResponse, UserPermissionsResponse,
    UserPermissionResponse, AssignPermissionRequest, AssignPermissionResponse,
    AssignPermissionsRequest, AssignPermissionsResponse, RevokePermissionResponse
)
from ..services.permission_service import PermissionService
from ..middleware.auth import get_current_user

router = APIRouter(prefix="/permissions", tags=["permissions"])

@router.get("/", response_model=List[PermissionResponse])
async def get_permissions(
    permission_service: PermissionService = Depends(),
    current_user: dict = Depends(get_current_user)
):
    permissions = permission_service.get_permissions()
    return [PermissionResponse.model_validate(p) for p in permissions]

@router.post("/", response_model=PermissionResponse)
async def create_permission(
    permission_data: PermissionCreate,
    permission_service: PermissionService = Depends(),
    current_user: dict = Depends(get_current_user)
):
    permission = permission_service.create_permission(permission_data.model_dump())
    return PermissionResponse.model_validate(permission)

@router.get("/users/{user_id}/permissions", response_model=UserPermissionsResponse)
async def get_user_permissions(
    user_id: str,
    permission_service: PermissionService = Depends(),
    current_user: dict = Depends(get_current_user)
):
    user_permissions = permission_service.get_user_permissions(user_id)
    
    permissions = [
        UserPermissionResponse(
            id=up.permission.id,
            name=up.permission.name,
            resource=up.permission.resource,
            action=up.permission.action,
            granted_at=up.granted_at
        )
        for up in user_permissions
    ]
    
    return UserPermissionsResponse(
        user_id=user_id,
        permissions=permissions
    )

@router.post("/users/{user_id}/permissions", response_model=AssignPermissionResponse)
async def assign_permission_to_user(
    user_id: str,
    permission_data: AssignPermissionRequest,
    permission_service: PermissionService = Depends(),
    current_user: dict = Depends(get_current_user)
):
    user_permission = permission_service.assign_permission_to_user(
        user_id=user_id,
        permission_id=permission_data.permission_id,
        granted_by=current_user["sub"]
    )
    
    return AssignPermissionResponse(user_permission_id=user_permission.id)

@router.post("/users/{user_id}/permissions/batch", response_model=AssignPermissionsResponse)
async def assign_permissions_to_user(
    user_id: str,
    permissions_data: AssignPermissionsRequest,
    permission_service: PermissionService = Depends(),
    current_user: dict = Depends(get_current_user)
):
    result = permission_service.assign_permissions_to_user(
        user_id=user_id,
        permission_ids=permissions_data.permission_ids,
        granted_by=current_user["sub"]
    )
    
    return AssignPermissionsResponse(
        user_permission_ids=result["user_permission_ids"],
        failed_assignments=result["failed_assignments"]
    )

@router.delete("/users/{user_id}/permissions/{permission_id}", response_model=RevokePermissionResponse)
async def revoke_permission_from_user(
    user_id: str,
    permission_id: str,
    permission_service: PermissionService = Depends(),
    current_user: dict = Depends(get_current_user)
):
    permission_service.revoke_permission_from_user(user_id, permission_id)
    return RevokePermissionResponse()