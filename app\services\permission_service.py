from sqlalchemy.orm import Session
from typing import List, Dict, Any
from ..models.permission import Permission
from ..models.user_permission import UserPermission
from ..database.session import get_db
from fastapi import Depends, HTTPException

class PermissionService:
    def __init__(self, db: Session = Depends(get_db)):
        self.db = db
    
    def create_permission(self, permission_data: dict) -> Permission:
        permission = Permission(**permission_data)
        self.db.add(permission)
        self.db.commit()
        self.db.refresh(permission)
        return permission
    
    def get_permissions(self) -> List[Permission]:
        return self.db.query(Permission).all()
    
    def get_permission_by_id(self, permission_id: str) -> Permission:
        permission = self.db.query(Permission).filter(Permission.id == permission_id).first()
        if not permission:
            raise HTTPException(status_code=404, detail="Permission not found")
        return permission
    
    def get_user_permissions(self, user_id: str) -> List[UserPermission]:
        return self.db.query(UserPermission).filter(UserPermission.user_id == user_id).all()
    
    def assign_permission_to_user(self, user_id: str, permission_id: str, granted_by: str) -> UserPermission:
        existing = self.db.query(UserPermission).filter(
            UserPermission.user_id == user_id,
            UserPermission.permission_id == permission_id
        ).first()
        
        if existing:
            raise HTTPException(status_code=400, detail="Permission already assigned to user")
        
        user_permission = UserPermission(
            user_id=user_id,
            permission_id=permission_id,
            granted_by=granted_by
        )
        self.db.add(user_permission)
        self.db.commit()
        self.db.refresh(user_permission)
        return user_permission
    
    def assign_permissions_to_user(self, user_id: str, permission_ids: List[str], granted_by: str) -> Dict[str, Any]:
        user_permission_ids = []
        failed_assignments = []
        
        for permission_id in permission_ids:
            try:
                existing = self.db.query(UserPermission).filter(
                    UserPermission.user_id == user_id,
                    UserPermission.permission_id == permission_id
                ).first()
                
                if existing:
                    failed_assignments.append({
                        "permission_id": permission_id,
                        "reason": "Permission already assigned to user"
                    })
                    continue
                
                permission_exists = self.db.query(Permission).filter(Permission.id == permission_id).first()
                if not permission_exists:
                    failed_assignments.append({
                        "permission_id": permission_id,
                        "reason": "Permission not found"
                    })
                    continue
                
                user_permission = UserPermission(
                    user_id=user_id,
                    permission_id=permission_id,
                    granted_by=granted_by
                )
                self.db.add(user_permission)
                self.db.flush()
                user_permission_ids.append(user_permission.id)
                
            except Exception as e:
                failed_assignments.append({
                    "permission_id": permission_id,
                    "reason": str(e)
                })
        
        if user_permission_ids:
            self.db.commit()
        
        return {
            "user_permission_ids": user_permission_ids,
            "failed_assignments": failed_assignments if failed_assignments else None
        }
    
    def revoke_permission_from_user(self, user_id: str, permission_id: str) -> bool:
        user_permission = self.db.query(UserPermission).filter(
            UserPermission.user_id == user_id,
            UserPermission.permission_id == permission_id
        ).first()
        
        if not user_permission:
            raise HTTPException(status_code=404, detail="Permission not assigned to user")
        
        self.db.delete(user_permission)
        self.db.commit()
        return True