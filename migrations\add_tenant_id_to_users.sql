-- Migration: Add tenant_id column to users table
-- Date: 2025-08-19
-- Description: Add tenant_id foreign key to users table for multi-tenancy support
--              Super admins will have NULL tenant_id (global access)
--              Tenant admins and other users will have specific tenant_id

-- Add tenant_id column (nullable to allow super admins)
ALTER TABLE users ADD COLUMN tenant_id VARCHAR REFERENCES tenants(id);

-- Create index for better query performance
CREATE INDEX idx_users_tenant_id ON users(tenant_id);

-- Update existing super admin users to have NULL tenant_id (if any exist with super admin role)
-- This ensures super admins have global access across all tenants
UPDATE users 
SET tenant_id = NULL 
WHERE role_id IN (
    SELECT id FROM roles WHERE code = 'SUPER_ADMIN'
);

-- Add comment to document the column purpose
COMMENT ON COLUMN users.tenant_id IS 'Foreign key to tenants table. NULL for super admins (global access), specific tenant_id for tenant-scoped users';