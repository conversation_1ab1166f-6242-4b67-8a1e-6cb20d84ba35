from fastapi import APIRouter, Depends, Query
from typing import List, Optional
from ..schemas.school import SchoolCreate, SchoolUpdate, SchoolResponse, SchoolStandardResponse, SchoolListStandardResponse, DeleteStandardResponse
from ..services.school_service import get_school_service, SchoolService
from ..middleware.auth import get_current_user_with_tenant
from ..utils.responses import success_response, paginated_response

router = APIRouter(prefix="/schools", tags=["schools"])

@router.get("/", response_model=SchoolListStandardResponse)
async def get_schools(
    page: int = Query(1, ge=1),
    size: int = Query(20, ge=1, le=100),
    current_user: dict = Depends(get_current_user_with_tenant),
    school_service: SchoolService = Depends(get_school_service)
):
    """Get list of schools with pagination filtered by current user's tenant"""
    skip = (page - 1) * size
    tenant_id = current_user["tenant_id"]
    schools = await school_service.get_schools(skip=skip, limit=size, tenant_id=tenant_id)
    total = await school_service.get_total_schools_count(tenant_id=tenant_id)
    
    return paginated_response(
        data=schools,
        total=total,
        page=page,
        size=size,
        message="Schools retrieved successfully"
    )

@router.get("/{school_id}", response_model=SchoolStandardResponse)
async def get_school(
    school_id: str,
    current_user: dict = Depends(get_current_user_with_tenant),
    school_service: SchoolService = Depends(get_school_service)
):
    """Get school by ID filtered by current user's tenant"""
    tenant_id = current_user["tenant_id"]
    school = await school_service.get_school_by_id(school_id, tenant_id)
    return success_response(data=school, message="School retrieved successfully")

@router.post("/", response_model=SchoolStandardResponse)
async def create_school(
    school_data: SchoolCreate,
    current_user: dict = Depends(get_current_user_with_tenant),
    school_service: SchoolService = Depends(get_school_service)
):
    """Create a new school in current user's tenant"""
    # Override tenant_id from current user
    school_data.tenant_id = current_user["tenant_id"]
    school = await school_service.create_school(school_data)
    return success_response(data=school, message="School created successfully")

@router.put("/{school_id}", response_model=SchoolStandardResponse)
async def update_school(
    school_id: str,
    school_data: SchoolUpdate,
    current_user: dict = Depends(get_current_user_with_tenant),
    school_service: SchoolService = Depends(get_school_service)
):
    """Update school information filtered by current user's tenant"""
    tenant_id = current_user["tenant_id"]
    school = await school_service.update_school(school_id, school_data, tenant_id)
    return success_response(data=school, message="School updated successfully")

@router.delete("/{school_id}", response_model=DeleteStandardResponse)
async def delete_school(
    school_id: str,
    current_user: dict = Depends(get_current_user_with_tenant),
    school_service: SchoolService = Depends(get_school_service)
):
    """Delete school filtered by current user's tenant"""
    tenant_id = current_user["tenant_id"]
    result = await school_service.delete_school(school_id, tenant_id)
    return success_response(data=result, message="School deleted successfully")