from pydantic import BaseModel
from typing import Optional
from datetime import datetime
from enum import Enum
from .responses import StandardResponse, PaginatedResponse


class PackageType(str, Enum):
    TRIAL = "trial"
    PRO = "pro"
    PRO_MAX = "pro_max"


class CycleType(str, Enum):
    SEVEN_DAYS = "7_days"
    ONE_MONTH = "1_month"
    SIX_MONTHS = "6_months"
    ONE_YEAR = "1_year"


class TenantBase(BaseModel):
    name: str
    package: PackageType
    expired_date: datetime
    cycle: CycleType
    manager_id: str
    phone: Optional[str] = None


class TenantCreate(TenantBase):
    created_by: str


class TenantUpdate(BaseModel):
    name: Optional[str] = None
    package: Optional[PackageType] = None
    expired_date: Optional[datetime] = None
    cycle: Optional[CycleType] = None
    manager_id: Optional[str] = None
    phone: Optional[str] = None


class TenantResponse(TenantBase):
    id: str
    created_by: str
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    model_config = {"from_attributes": True}


# Type aliases for standardized responses
TenantStandardResponse = StandardResponse[TenantResponse]
TenantListStandardResponse = PaginatedResponse[TenantResponse]
DeleteStandardResponse = StandardResponse[dict]