from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import Dict, List
from ..services.seed_service import get_seed_service, SeedService
from ..schemas.role import RoleResponse
from ..schemas.user import UserResponse
from ..schemas.permission import PermissionResponse
from ..database.session import get_db

router = APIRouter(prefix="/seed", tags=["seed"])

@router.post("/roles", response_model=List[RoleResponse])
async def seed_roles(seed_service: SeedService = Depends(get_seed_service)):
    """Seed default roles"""
    roles = seed_service.seed_default_roles()
    return [
        RoleResponse(
            id=role.id,
            name=role.name,
            code=role.code
        )
        for role in roles
    ]

@router.post("/permissions", response_model=List[PermissionResponse])
async def seed_permissions(seed_service: SeedService = Depends(get_seed_service)):
    """Seed default permissions"""
    permissions = seed_service.seed_default_permissions()
    return [
        PermissionResponse(
            id=permission.id,
            name=permission.name,
            resource=permission.resource,
            action=permission.action,
            description=permission.description
        )
        for permission in permissions
    ]

@router.post("/admin-user", response_model=UserResponse)
async def seed_admin_user(seed_service: SeedService = Depends(get_seed_service)):
    """Seed default admin user"""
    user = await seed_service.seed_admin_user()
    return UserResponse(
        id=user.id,
        keycloak_id=user.keycloak_id,
        full_name=user.full_name,
        email=user.email,
        username=user.username,
        role_id=user.role_id,
        created_at=user.created_at,
        updated_at=user.updated_at
    )

@router.post("/admin-permissions")
async def assign_admin_permissions(seed_service: SeedService = Depends(get_seed_service)) -> Dict[str, str]:
    """Assign all permissions to the admin user"""
    from ..models.user import User
    
    # Get the admin user
    admin_user = seed_service.db.query(User).filter(User.username == "trungadmin").first()
    
    if not admin_user:
        raise HTTPException(status_code=404, detail="Admin user not found. Please seed admin user first.")
    
    try:
        assigned_count = seed_service.assign_all_permissions_to_user(admin_user)
        return {
            "message": f"Successfully assigned {assigned_count} permissions to admin user",
            "user": admin_user.username,
            "permissions_assigned": assigned_count
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to assign permissions: {str(e)}")

@router.post("/all")
async def seed_all(seed_service: SeedService = Depends(get_seed_service)) -> Dict[str, str]:
    """Run all seed functions"""
    return await seed_service.run_all_seeds()

@router.get("/status")
async def seed_status(db: Session = Depends(get_db)) -> Dict[str, bool]:
    """Check seed status - whether default data exists"""
    from ..models.role import Role
    from ..models.user import User
    from ..models.permission import Permission
    
    # Check if roles exist
    roles_exist = db.query(Role).filter(Role.code == "SUPER_ADMIN").first() is not None
    
    # Check if permissions exist
    permissions_exist = db.query(Permission).filter(Permission.resource == "users").first() is not None
    
    # Check if admin user exists
    admin_exists = db.query(User).filter(User.username == "trungadmin").first() is not None
    
    return {
        "roles_seeded": roles_exist,
        "permissions_seeded": permissions_exist,
        "admin_user_seeded": admin_exists,
        "all_seeded": roles_exist and permissions_exist and admin_exists
    }