from keycloak import Key<PERSON><PERSON>akAdmin, KeycloakOpenID
from fastapi import HTTPException
from typing import Dict, List, Optional
from ..core.config import settings
from ..utils.responses import APIException, ErrorCodes

class KeycloakService:
    def __init__(self):
        self.keycloak_openid = KeycloakOpenID(
            server_url=settings.keycloak_server_url,
            client_id=settings.keycloak_client_id,
            realm_name=settings.keycloak_realm,
            client_secret_key=settings.keycloak_client_secret
        )
        
        self.keycloak_admin = KeycloakAdmin(
            server_url=settings.keycloak_server_url,
            username=settings.keycloak_admin_username,
            password=settings.keycloak_admin_password,
            realm_name=settings.keycloak_realm,
            verify=True
        )
    
    async def create_user(self, user_data: Dict) -> str:
        try:
            user_id = self.keycloak_admin.create_user({
                "email": user_data["email"],
                "username": user_data["username"],
                "enabled": True,
                "firstName": user_data.get("first_name"),
                "lastName": user_data.get("last_name"),
                "credentials": [{
                    "type": "password",
                    "value": user_data["password"],
                    "temporary": False
                }]
            })
            return user_id
        except Exception as e:
            raise APIException(
                status_code=400, 
                error_code=ErrorCodes.USER_INVALID_DATA, 
                message="Failed to create user in Keycloak",
                details={"keycloak_error": str(e)}
            )
    
    async def authenticate_user(self, username: str, password: str) -> Dict:
        try:
            token = self.keycloak_openid.token(username, password)
            return token
        except Exception as e:
            import logging
            logger = logging.getLogger("uvicorn.error")
            logger.error(f"Keycloak authentication error: {str(e)}")
            logger.error(f"Keycloak config - Server: {settings.keycloak_server_url}, Realm: {settings.keycloak_realm}, Client: {settings.keycloak_client_id}")
            raise APIException(
                status_code=401,
                error_code=ErrorCodes.AUTH_INVALID_CREDENTIALS,
                message="Invalid credentials",
                details={"keycloak_error": str(e)}
            )
    
    async def verify_token(self, token: str) -> Dict:
        try:
            token_info = self.keycloak_openid.introspect(token)
            if not token_info.get("active"):
                raise HTTPException(status_code=401, detail="Token is not active")
            return token_info
        except Exception as e:
            raise HTTPException(status_code=401, detail="Invalid token")
    
    async def refresh_token(self, refresh_token: str) -> Dict:
        try:
            token = self.keycloak_openid.refresh_token(refresh_token)
            return token
        except Exception as e:
            raise HTTPException(status_code=401, detail="Invalid refresh token")
    
    async def logout_user(self, refresh_token: str) -> bool:
        try:
            self.keycloak_openid.logout(refresh_token)
            return True
        except Exception as e:
            raise HTTPException(status_code=400, detail=str(e))
    
    async def update_user(self, user_id: str, user_data: Dict) -> bool:
        try:
            self.keycloak_admin.update_user(user_id, user_data)
            return True
        except Exception as e:
            raise HTTPException(status_code=400, detail=str(e))
    
    async def delete_user(self, user_id: str) -> bool:
        try:
            self.keycloak_admin.delete_user(user_id)
            return True
        except Exception as e:
            raise HTTPException(status_code=400, detail=str(e))
    
    async def get_user(self, user_id: str) -> Dict:
        try:
            user = self.keycloak_admin.get_user(user_id)
            return user
        except Exception as e:
            raise HTTPException(status_code=404, detail="User not found")
    
    async def get_users(self) -> List[Dict]:
        try:
            users = self.keycloak_admin.get_users({})
            return users
        except Exception as e:
            raise HTTPException(status_code=400, detail=str(e))

keycloak_service = KeycloakService()