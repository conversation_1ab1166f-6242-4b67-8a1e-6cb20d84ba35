from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import List
from sqlalchemy.orm import Session
from ..services.keycloak_service import keycloak_service
from ..services.permission_service import PermissionService
from ..models.user import User
from ..database.session import get_db

security = HTTPBearer()

async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> dict:
    token = credentials.credentials
    token_info = await keycloak_service.verify_token(token)
    return token_info

class PermissionChecker:
    def __init__(self, required_permissions: List[str]):
        self.required_permissions = required_permissions
    
    async def __call__(
        self,
        credentials: HTTPAuthorizationCredentials = Depends(security),
        permission_service: PermissionService = Depends()
    ):
        token = credentials.credentials
        
        token_info = await keycloak_service.verify_token(token)
        user_id = token_info.get("sub")
        
        user_permissions = permission_service.get_user_permissions(user_id)
        user_permission_codes = [
            f"{p.permission.resource}:{p.permission.action}" 
            for p in user_permissions
        ]
        
        for required in self.required_permissions:
            if required not in user_permission_codes:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Missing required permission: {required}"
                )
        
        return token_info

def require_permissions(permissions: List[str]):
    return PermissionChecker(permissions)

async def get_current_user_with_role(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> dict:
    """Get current user with role and tenant information for hierarchical access control"""
    from ..models.role import Role
    
    token = credentials.credentials
    token_info = await keycloak_service.verify_token(token)
    keycloak_user_id = token_info.get("sub")
    
    # Get user from local database with role
    user = db.query(User).join(Role).filter(User.keycloak_id == keycloak_user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found in local database"
        )
    
    # Determine user permissions level
    role_code = user.role.code if user.role else None
    is_super_admin = role_code == "SUPER_ADMIN"
    is_tenant_admin = role_code == "TENANT_ADMIN" 
    is_school_admin = role_code == "SCHOOL_ADMIN"
    
    # Add enhanced user info to token
    token_info.update({
        "local_user_id": user.id,
        "tenant_id": user.tenant_id,  # NULL for super admins
        "role_code": role_code,
        "is_super_admin": is_super_admin,
        "is_tenant_admin": is_tenant_admin,
        "is_school_admin": is_school_admin,
        "full_name": user.full_name,
        "email": user.email
    })
    
    return token_info

async def get_current_user_with_tenant(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> dict:
    """Get current user token info along with tenant_id from local database"""
    token = credentials.credentials
    token_info = await keycloak_service.verify_token(token)
    keycloak_user_id = token_info.get("sub")
    
    # Get user from local database to retrieve tenant_id
    user = db.query(User).filter(User.keycloak_id == keycloak_user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found in local database"
        )
    
    # Add tenant_id and local user_id to token info
    token_info["tenant_id"] = user.tenant_id
    token_info["local_user_id"] = user.id
    
    return token_info

# Hierarchical permission checkers
def require_super_admin():
    """Middleware requiring super admin role"""
    async def check_super_admin(current_user: dict = Depends(get_current_user_with_role)):
        if not current_user.get("is_super_admin"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Super admin access required"
            )
        return current_user
    return Depends(check_super_admin)

def require_tenant_admin_or_above():
    """Middleware requiring tenant admin or super admin role"""
    async def check_tenant_admin(current_user: dict = Depends(get_current_user_with_role)):
        if not (current_user.get("is_super_admin") or current_user.get("is_tenant_admin")):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Tenant admin or super admin access required"
            )
        return current_user
    return Depends(check_tenant_admin)

def require_school_admin_or_above():
    """Middleware requiring school admin, tenant admin, or super admin role"""
    async def check_school_admin(current_user: dict = Depends(get_current_user_with_role)):
        if not (current_user.get("is_super_admin") or 
               current_user.get("is_tenant_admin") or 
               current_user.get("is_school_admin")):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Administrative access required"
            )
        return current_user
    return Depends(check_school_admin)