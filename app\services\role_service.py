from sqlalchemy.orm import Session
from typing import List
from ..models.role import Role
from ..database.session import get_db
from fastapi import Depends, HTTPException

class RoleService:
    def __init__(self, db: Session = Depends(get_db)):
        self.db = db
    
    def create_role(self, role_data: dict) -> Role:
        existing_role = self.db.query(Role).filter(
            (Role.name == role_data["name"]) | (Role.code == role_data["code"])
        ).first()
        
        if existing_role:
            raise HTTPException(status_code=400, detail="Role name or code already exists")
        
        role = Role(**role_data)
        self.db.add(role)
        self.db.commit()
        self.db.refresh(role)
        return role
    
    def get_roles(self) -> List[Role]:
        return self.db.query(Role).all()
    
    def get_role_by_id(self, role_id: str) -> Role:
        role = self.db.query(Role).filter(Role.id == role_id).first()
        if not role:
            raise HTTPException(status_code=404, detail="Role not found")
        return role