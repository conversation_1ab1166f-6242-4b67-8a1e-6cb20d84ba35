#!/usr/bin/env python3
import jwt
from datetime import datetime, timedelta

def generate_test_token():
    payload = {
        'sub': 'test-user-123',
        'username': 'testuser',
        'exp': datetime.utcnow() + timedelta(hours=1),
        'iat': datetime.utcnow()
    }
    token = jwt.encode(payload, 'test-secret', algorithm='HS256')
    return token

if __name__ == "__main__":
    token = generate_test_token()
    print("Bearer Token:")
    print(token)
    print()
    print("Usage:")
    print(f'curl -H "Authorization: Bearer {token}" http://localhost:8000/api/users')