from pydantic_settings import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    # Database settings
    database_url: str = "postgresql://user:password@localhost:5432/clover_erp"
    
    # Keycloak settings
    keycloak_server_url: str = "http://localhost:8080"
    keycloak_realm: str = "clover-realm"
    keycloak_client_id: str = "fastapi-backend"
    keycloak_client_secret: str = ""
    keycloak_admin_username: str = "admin"
    keycloak_admin_password: str = "admin"
    
    # API settings
    api_title: str = "Clover ERP API"
    api_description: str = "FastAPI backend with Keycloak integration"
    api_version: str = "1.0.0"
    
    # Application settings
    debug: bool = False
    
    # Security settings
    cors_origins: list[str] = ["*"]
    cors_credentials: bool = True
    cors_methods: list[str] = ["*"]
    cors_headers: list[str] = ["*"]
    
    # Server settings
    host: str = "0.0.0.0"
    port: int = 8000
    
    model_config = {
        "env_file": ".env",
        "case_sensitive": False,
        "extra": "ignore"
    }

settings = Settings()