from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime

class PermissionBase(BaseModel):
    name: str
    resource: str
    action: str
    description: Optional[str] = None

class PermissionCreate(PermissionBase):
    pass

class PermissionResponse(PermissionBase):
    id: str

    model_config = {"from_attributes": True}

class UserPermissionResponse(BaseModel):
    id: str
    name: str
    resource: str
    action: str
    granted_at: datetime

class UserPermissionsResponse(BaseModel):
    user_id: str
    permissions: list[UserPermissionResponse]

class AssignPermissionRequest(BaseModel):
    permission_id: str

class AssignPermissionsRequest(BaseModel):
    permission_ids: List[str]

class AssignPermissionResponse(BaseModel):
    message: str = "Permission successfully assigned"
    user_permission_id: str

class AssignPermissionsResponse(BaseModel):
    message: str = "Permissions successfully assigned"
    user_permission_ids: List[str]
    failed_assignments: Optional[List[dict]] = None

class RevokePermissionResponse(BaseModel):
    message: str = "Permission successfully revoked"