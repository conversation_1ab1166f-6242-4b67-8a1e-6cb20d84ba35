from sqlalchemy import Column, String, DateTime, ForeignKey, Enum as SQLEnum
from sqlalchemy.orm import relationship
from datetime import datetime
from enum import Enum
from .base import BaseModel


class PackageType(str, Enum):
    TRIAL = "trial"
    PRO = "pro"
    PRO_MAX = "pro_max"


class CycleType(str, Enum):
    SEVEN_DAYS = "7_days"
    ONE_MONTH = "1_month"
    SIX_MONTHS = "6_months"
    ONE_YEAR = "1_year"


class Tenant(BaseModel):
    __tablename__ = "tenants"
    
    name = Column(String, nullable=False)
    package = Column(SQLEnum(PackageType), nullable=False, default=PackageType.TRIAL)
    expired_date = Column(DateTime, nullable=False)
    cycle = Column(SQLEnum(CycleType), nullable=False, default=CycleType.ONE_MONTH)
    manager_id = Column(String, ForeignKey("users.id"), nullable=False)
    phone = Column(String, nullable=True)
    created_by = Column(String, nullable=False)
    
    # Relationships
    manager = relationship("User", foreign_keys=[manager_id], post_update=True)
    users = relationship("User", back_populates="tenant", primaryjoin="Tenant.id == User.tenant_id", overlaps="tenant")