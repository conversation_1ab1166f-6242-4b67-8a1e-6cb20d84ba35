from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
import time
import logging
from .core.config import settings
from .routers import auth, users, permissions, roles, mock_auth, test_endpoints, seed, tenants, schools
from .models.base import Base
from .database.session import engine
from .utils.responses import (
    APIException, 
    api_exception_handler, 
    http_exception_handler, 
    validation_exception_handler,
    success_response,
    ErrorCodes
)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

Base.metadata.create_all(bind=engine)

app = FastAPI(
    title=settings.api_title,
    description=settings.api_description,
    version=settings.api_version,
    debug=settings.debug
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=settings.cors_credentials,
    allow_methods=settings.cors_methods,
    allow_headers=settings.cors_headers,
)

# Register custom exception handlers
app.add_exception_handler(APIException, api_exception_handler)
app.add_exception_handler(HTTPException, http_exception_handler)
app.add_exception_handler(RequestValidationError, validation_exception_handler)

@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = time.time()
    
    response = await call_next(request)
    
    process_time = time.time() - start_time
    logger.info(
        f"Path: {request.url.path} | "
        f"Method: {request.method} | "
        f"Status: {response.status_code} | "
        f"Duration: {process_time:.3f}s"
    )
    
    return response

@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    logger.error(f"Global exception: {str(exc)}")
    from datetime import datetime
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "error": {
                "code": ErrorCodes.SERVER_ERROR,
                "message": "Internal server error",
                "details": {"exception": str(exc)}
            },
            "timestamp": datetime.utcnow().isoformat(),
            "path": str(request.url.path)
        }
    )

app.include_router(auth.router, prefix="/api")
app.include_router(mock_auth.router, prefix="/api")
app.include_router(test_endpoints.router, prefix="/api")
app.include_router(users.router, prefix="/api")
app.include_router(permissions.router, prefix="/api")
app.include_router(roles.router, prefix="/api")
app.include_router(tenants.router, prefix="/api")
app.include_router(schools.router, prefix="/api")
app.include_router(seed.router, prefix="/api")

@app.get("/")
async def root():
    return success_response(
        data={
            "message": "Clover ERP API",
            "version": settings.api_version,
            "status": "running"
        },
        message="API is running successfully"
    )

@app.get("/health")
async def health_check():
    return success_response(
        data={"status": "healthy"},
        message="Health check passed"
    )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host=settings.host, port=settings.port)