from sqlalchemy.orm import Session
from typing import List, Dict
from ..models.role import Role
from ..models.user import User
from ..models.permission import Permission
from ..models.user_permission import UserPermission
from ..database.session import get_db
from .keycloak_service import keycloak_service
from ..schemas.user import User<PERSON>reate
from fastapi import Depends, HTTPException
import logging

logger = logging.getLogger("uvicorn.error")

class SeedService:
    def __init__(self, db: Session):
        self.db = db
        self.keycloak_service = keycloak_service
    
    def seed_default_roles(self) -> List[Role]:
        """Create default roles if they don't exist"""
        default_roles = [
            {"name": "Super Admin", "code": "SUPER_ADMIN"},
            {"name": "Tenant Admin", "code": "TENANT_ADMIN"},
            {"name": "School Admin", "code": "SCHOOL_ADMIN"},
            {"name": "Teacher", "code": "TEACHER"},
            {"name": "Accountant", "code": "ACCOUNTANT"},
            {"name": "Kitchen Staff", "code": "KITCHEN_STAFF"},
            {"name": "Physical Education Teacher", "code": "PE_TEACHER"},
            {"name": "Parents", "code": "PARENTS"}
        ]
        
        created_roles = []
        
        for role_data in default_roles:
            # Check if role already exists
            existing_role = self.db.query(Role).filter(
                (Role.name == role_data["name"]) | (Role.code == role_data["code"])
            ).first()
            
            if not existing_role:
                role = Role(**role_data)
                self.db.add(role)
                created_roles.append(role)
                logger.info(f"Created role: {role_data['name']} ({role_data['code']})")
            else:
                logger.info(f"Role already exists: {role_data['name']} ({role_data['code']})")
                created_roles.append(existing_role)
        
        try:
            self.db.commit()
            for role in created_roles:
                self.db.refresh(role)
            logger.info(f"Successfully seeded {len(created_roles)} roles")
            return created_roles
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to seed roles: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to seed roles: {str(e)}")
    
    async def seed_admin_user(self) -> User:
        """Create default admin user if it doesn't exist"""
        admin_data = {
            "username": "trungadmin",
            "password": "0986575238Trung$",
            "full_name": "Bui Quang Trung",
            "email": "<EMAIL>"
        }
        
        # Check if user already exists in local database
        existing_user = self.db.query(User).filter(
            (User.username == admin_data["username"]) | 
            (User.email == admin_data["email"])
        ).first()
        
        if existing_user:
            logger.info(f"Admin user already exists: {admin_data['username']}")
            # Ensure existing admin user has all permissions
            try:
                assigned_permissions = self.assign_all_permissions_to_user(existing_user)
                if assigned_permissions > 0:
                    logger.info(f"Assigned {assigned_permissions} additional permissions to existing admin user")
                else:
                    logger.info(f"Existing admin user already has all permissions")
            except Exception as perm_error:
                logger.warning(f"Failed to assign permissions to existing admin user: {str(perm_error)}")
            return existing_user
        
        try:
            # Get Super Admin role
            super_admin_role = self.db.query(Role).filter(Role.code == "SUPER_ADMIN").first()
            if not super_admin_role:
                raise HTTPException(status_code=404, detail="Super Admin role not found. Please seed roles first.")
            
            # Split full name for Keycloak
            names = admin_data["full_name"].split(" ", 1)
            first_name = names[0] if names else ""
            last_name = names[1] if len(names) > 1 else ""
            
            # Create user in Keycloak
            keycloak_user_data = {
                "email": admin_data["email"],
                "username": admin_data["username"],
                "password": admin_data["password"],
                "first_name": first_name,
                "last_name": last_name
            }
            
            logger.info(f"Creating admin user in Keycloak: {admin_data['username']}")
            try:
                keycloak_user_id = await self.keycloak_service.create_user(keycloak_user_data)
                logger.info(f"Keycloak user created with ID: {keycloak_user_id}")
            except HTTPException as ke:
                logger.error(f"Keycloak user creation failed: {ke.detail}")
                raise HTTPException(status_code=500, detail=f"Keycloak user creation failed: {ke.detail}")
            except Exception as ke:
                logger.error(f"Keycloak user creation error: {str(ke)}")
                raise HTTPException(status_code=500, detail=f"Keycloak user creation error: {str(ke)}")
            
            # Create user in local database
            db_user = User(
                keycloak_id=keycloak_user_id,
                full_name=admin_data["full_name"],
                email=admin_data["email"],
                username=admin_data["username"],
                role_id=super_admin_role.id
            )
            
            self.db.add(db_user)
            self.db.commit()
            self.db.refresh(db_user)
            
            # Assign all permissions to the admin user
            try:
                assigned_permissions = self.assign_all_permissions_to_user(db_user)
                logger.info(f"Assigned {assigned_permissions} permissions to admin user: {admin_data['username']}")
            except Exception as perm_error:
                logger.warning(f"Failed to assign permissions to admin user: {str(perm_error)}")
                # Don't fail the entire process if permission assignment fails
            
            logger.info(f"Successfully created admin user: {admin_data['username']}")
            return db_user
            
        except HTTPException as he:
            # This is already an HTTPException from Keycloak service
            logger.error(f"Keycloak HTTPException: {he.detail}")
            self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Keycloak error: {he.detail}")
        except Exception as e:
            # If local DB creation fails, try to cleanup Keycloak user
            if 'keycloak_user_id' in locals():
                try:
                    await self.keycloak_service.delete_user(keycloak_user_id)
                    logger.info(f"Cleaned up Keycloak user after error: {keycloak_user_id}")
                except Exception as cleanup_error:
                    logger.error(f"Failed to cleanup Keycloak user: {str(cleanup_error)}")
            
            self.db.rollback()
            logger.error(f"Failed to create admin user: {str(e)}")
            logger.error(f"Exception type: {type(e).__name__}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            raise HTTPException(status_code=500, detail=f"Failed to create admin user: {str(e)} - Type: {type(e).__name__}")
    
    def seed_default_permissions(self) -> List[Permission]:
        """Create default permissions if they don't exist"""
        default_permissions = [
            # Users permissions
            {"name": "View Users", "resource": "users", "action": "view", "description": "Permission to view users"},
            {"name": "Create Users", "resource": "users", "action": "create", "description": "Permission to create users"},
            {"name": "Update Users", "resource": "users", "action": "update", "description": "Permission to update users"},
            {"name": "Delete Users", "resource": "users", "action": "delete", "description": "Permission to delete users"},
            
            # Tenants permissions
            {"name": "View Tenants", "resource": "tenants", "action": "view", "description": "Permission to view tenants"},
            {"name": "Create Tenants", "resource": "tenants", "action": "create", "description": "Permission to create tenants"},
            {"name": "Update Tenants", "resource": "tenants", "action": "update", "description": "Permission to update tenants"},
            {"name": "Delete Tenants", "resource": "tenants", "action": "delete", "description": "Permission to delete tenants"},
            
            # Schools permissions
            {"name": "View Schools", "resource": "schools", "action": "view", "description": "Permission to view schools"},
            {"name": "Create Schools", "resource": "schools", "action": "create", "description": "Permission to create schools"},
            {"name": "Update Schools", "resource": "schools", "action": "update", "description": "Permission to update schools"},
            {"name": "Delete Schools", "resource": "schools", "action": "delete", "description": "Permission to delete schools"},
        ]
        
        created_permissions = []
        
        for perm_data in default_permissions:
            # Check if permission already exists
            existing_permission = self.db.query(Permission).filter(
                (Permission.resource == perm_data["resource"]) & 
                (Permission.action == perm_data["action"])
            ).first()
            
            if not existing_permission:
                permission = Permission(**perm_data)
                self.db.add(permission)
                created_permissions.append(permission)
                logger.info(f"Created permission: {perm_data['resource']}.{perm_data['action']}")
            else:
                logger.info(f"Permission already exists: {perm_data['resource']}.{perm_data['action']}")
                created_permissions.append(existing_permission)
        
        try:
            self.db.commit()
            for permission in created_permissions:
                self.db.refresh(permission)
            logger.info(f"Successfully seeded {len(created_permissions)} permissions")
            return created_permissions
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to seed permissions: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to seed permissions: {str(e)}")
    
    def assign_all_permissions_to_user(self, user: User) -> int:
        """Assign all available permissions to a user"""
        try:
            # Get all permissions
            all_permissions = self.db.query(Permission).all()
            
            # Check which permissions the user already has
            existing_user_permissions = self.db.query(UserPermission).filter(
                UserPermission.user_id == user.id
            ).all()
            existing_permission_ids = {up.permission_id for up in existing_user_permissions}
            
            # Assign missing permissions
            assigned_count = 0
            for permission in all_permissions:
                if permission.id not in existing_permission_ids:
                    user_permission = UserPermission(
                        user_id=user.id,
                        permission_id=permission.id,
                        granted_by="system_seed"
                    )
                    self.db.add(user_permission)
                    assigned_count += 1
                    logger.info(f"Assigned permission {permission.resource}.{permission.action} to user {user.username}")
            
            if assigned_count > 0:
                self.db.commit()
                logger.info(f"Successfully assigned {assigned_count} permissions to user {user.username}")
            else:
                logger.info(f"User {user.username} already has all permissions")
            
            return assigned_count
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to assign permissions to user {user.username}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to assign permissions: {str(e)}")
    
    async def run_all_seeds(self) -> Dict[str, str]:
        """Run all seed functions"""
        results = {}
        
        try:
            # Seed roles first
            roles = self.seed_default_roles()
            results["roles"] = f"Seeded {len(roles)} roles successfully"
            
            # Seed permissions second (before admin user)
            permissions = self.seed_default_permissions()
            results["permissions"] = f"Seeded {len(permissions)} permissions successfully"
            
            # Seed admin user (will auto-assign all permissions)
            admin_user = await self.seed_admin_user()
            results["admin_user"] = f"Seeded admin user: {admin_user.username}"
            
            # Ensure existing admin user has all permissions (in case permissions were added later)
            if admin_user:
                try:
                    assigned_permissions = self.assign_all_permissions_to_user(admin_user)
                    if assigned_permissions > 0:
                        results["admin_permissions"] = f"Assigned {assigned_permissions} additional permissions to admin user"
                    else:
                        results["admin_permissions"] = "Admin user already has all permissions"
                except Exception as perm_error:
                    results["admin_permissions"] = f"Warning: Could not verify admin permissions: {str(perm_error)}"
            
            results["status"] = "success"
            logger.info("All seeds completed successfully")
            
        except Exception as e:
            results["status"] = "error"
            results["error"] = str(e)
            logger.error(f"Seed process failed: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Seed process failed: {str(e)}")
        
        return results


def get_seed_service(db: Session = Depends(get_db)) -> SeedService:
    return SeedService(db)