#!/usr/bin/env python3
"""
Test script specifically for the admin permissions endpoint fix
"""
import requests
import json

BASE_URL = "http://127.0.0.1:8000/api"

def test_admin_permissions_endpoint():
    """Test the admin permissions endpoint specifically"""
    print("Testing admin permissions endpoint...")
    
    try:
        # First, check seed status
        print("1. Checking seed status...")
        response = requests.get(f"{BASE_URL}/seed/status")
        if response.status_code == 200:
            status = response.json()
            print(f"   Seed status: {json.dumps(status, indent=4)}")
        else:
            print(f"   Failed to get seed status: {response.status_code}")
        
        # Test the admin permissions endpoint
        print("\n2. Testing admin permissions assignment...")
        response = requests.post(f"{BASE_URL}/seed/admin-permissions")
        
        if response.status_code == 200:
            result = response.json()
            print("   ✅ SUCCESS! Admin permissions endpoint working:")
            print(f"      Message: {result.get('message', 'N/A')}")
            print(f"      User: {result.get('user', 'N/A')}")
            print(f"      Permissions assigned: {result.get('permissions_assigned', 'N/A')}")
        elif response.status_code == 404:
            print("   ⚠️  Admin user not found. Let's seed the admin user first...")
            
            # Try to seed the admin user
            print("\n3. Seeding admin user...")
            seed_response = requests.post(f"{BASE_URL}/seed/admin-user")
            if seed_response.status_code == 200:
                print("   ✅ Admin user seeded successfully")
                
                # Try admin permissions again
                print("\n4. Retrying admin permissions assignment...")
                retry_response = requests.post(f"{BASE_URL}/seed/admin-permissions")
                if retry_response.status_code == 200:
                    result = retry_response.json()
                    print("   ✅ SUCCESS! Admin permissions working after seeding:")
                    print(f"      Message: {result.get('message', 'N/A')}")
                    print(f"      User: {result.get('user', 'N/A')}")
                    print(f"      Permissions assigned: {result.get('permissions_assigned', 'N/A')}")
                else:
                    print(f"   ❌ Still failed: {retry_response.status_code}")
                    print(f"   Response: {retry_response.text}")
            else:
                print(f"   ❌ Failed to seed admin user: {seed_response.status_code}")
                print(f"   Response: {seed_response.text}")
        else:
            print(f"   ❌ Failed: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to the API. Make sure the server is running on http://127.0.0.1:8000")
    except Exception as e:
        print(f"❌ Error testing admin permissions: {str(e)}")

if __name__ == "__main__":
    print("=" * 60)
    print("Testing Admin Permissions Endpoint Fix")
    print("=" * 60)
    
    test_admin_permissions_endpoint()
    
    print("\n" + "=" * 60)
    print("Test completed!")