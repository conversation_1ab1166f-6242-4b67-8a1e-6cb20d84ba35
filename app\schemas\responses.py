from pydantic import BaseModel
from typing import Generic, TypeVar, Optional, Any, Dict
from datetime import datetime

T = TypeVar('T')

class ErrorDetail(BaseModel):
    code: str
    message: str
    details: Optional[Dict[str, Any]] = None

class ErrorResponse(BaseModel):
    error: ErrorDetail
    timestamp: datetime
    path: str

class SuccessResponse(BaseModel, Generic[T]):
    success: bool = True
    data: T
    message: Optional[str] = None
    timestamp: datetime = datetime.utcnow()

class StandardResponse(BaseModel, Generic[T]):
    success: bool
    data: Optional[T] = None
    error: Optional[ErrorDetail] = None
    message: Optional[str] = None
    timestamp: datetime = datetime.utcnow()
    
    @classmethod
    def success_response(cls, data: T, message: Optional[str] = None) -> "StandardResponse[T]":
        return cls(
            success=True,
            data=data,
            message=message,
            timestamp=datetime.utcnow()
        )
    
    @classmethod
    def error_response(cls, code: str, message: str, details: Optional[Dict[str, Any]] = None) -> "StandardResponse[None]":
        return cls(
            success=False,
            error=ErrorDetail(
                code=code,
                message=message,
                details=details
            ),
            timestamp=datetime.utcnow()
        )

class PaginatedResponse(BaseModel, Generic[T]):
    success: bool = True
    data: list[T]
    total: int
    page: int
    size: int
    total_pages: int
    message: Optional[str] = None
    timestamp: datetime = datetime.utcnow()
    
    @classmethod
    def create(cls, data: list[T], total: int, page: int, size: int, message: Optional[str] = None) -> "PaginatedResponse[T]":
        total_pages = (total + size - 1) // size
        return cls(
            data=data,
            total=total,
            page=page,
            size=size,
            total_pages=total_pages,
            message=message,
            timestamp=datetime.utcnow()
        )