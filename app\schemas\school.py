from pydantic import BaseModel
from typing import Optional
from datetime import datetime
from .responses import StandardResponse, PaginatedResponse


class SchoolBase(BaseModel):
    name: str
    code: str
    address: Optional[str] = None
    phone: Optional[str] = None
    tenant_id: str
    manager_id: str


class SchoolCreate(SchoolBase):
    pass


class SchoolUpdate(BaseModel):
    name: Optional[str] = None
    code: Optional[str] = None
    address: Optional[str] = None
    phone: Optional[str] = None
    manager_id: Optional[str] = None


class SchoolResponse(SchoolBase):
    id: str
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    model_config = {"from_attributes": True}


# Type aliases for standardized responses
SchoolStandardResponse = StandardResponse[SchoolResponse]
SchoolListStandardResponse = PaginatedResponse[SchoolResponse]
DeleteStandardResponse = StandardResponse[dict]