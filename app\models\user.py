from sqlalchemy import Column, String, ForeignKey
from sqlalchemy.orm import relationship
from .base import BaseModel


class User(BaseModel):
    __tablename__ = "users"
    
    keycloak_id = Column(String, nullable=False, unique=True, index=True)  # Reference to Keycloak user ID
    full_name = Column(String, nullable=False)
    email = Column(String, nullable=False, unique=True, index=True)
    username = Column(String, nullable=False, unique=True, index=True)
    role_id = Column(String, ForeignKey("roles.id"), nullable=True)
    tenant_id = Column(String, ForeignKey("tenants.id"), nullable=True)  # NULL for super admins (global access)
    
    # Relationships
    role = relationship("Role", back_populates="users")
    tenant = relationship("Tenant", back_populates="users", primaryjoin="User.tenant_id == Tenant.id", foreign_keys=[tenant_id])
    user_permissions = relationship("UserPermission", back_populates="user")