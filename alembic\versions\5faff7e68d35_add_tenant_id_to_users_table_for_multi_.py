"""Add tenant_id to users table for multi-tenancy support

Revision ID: 5faff7e68d35
Revises: 
Create Date: 2025-08-19 20:50:56.637667

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '5faff7e68d35'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_permissions', sa.Column('tenant_id', sa.String(), nullable=True))
    op.create_foreign_key(None, 'user_permissions', 'users', ['user_id'], ['id'])
    op.create_foreign_key(None, 'user_permissions', 'tenants', ['tenant_id'], ['id'])
    op.add_column('users', sa.Column('tenant_id', sa.String(), nullable=True))
    op.create_foreign_key(None, 'users', 'tenants', ['tenant_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'users', type_='foreignkey')
    op.drop_column('users', 'tenant_id')
    op.drop_constraint(None, 'user_permissions', type_='foreignkey')
    op.drop_constraint(None, 'user_permissions', type_='foreignkey')
    op.drop_column('user_permissions', 'tenant_id')
    # ### end Alembic commands ###
