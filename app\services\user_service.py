from fastapi import HTT<PERSON>Ex<PERSON>, Depends
from sqlalchemy.orm import Session
from typing import Dict, List, Optional
from ..models.user import User
from ..models.role import Role
from ..database.session import get_db
from .keycloak_service import keycloak_service
from ..schemas.user import UserCreate, UserUpdate, UserResponse


class UserService:
    def __init__(self, db: Session = Depends(get_db)):
        self.db = db
        self.keycloak_service = keycloak_service
    
    async def create_user(self, user_data: UserCreate) -> UserResponse:
        """Create user in both Keycloak and local database"""
        try:
            # First, create user in Keycloak
            names = user_data.full_name.split(" ", 1)
            first_name = names[0] if names else ""
            last_name = names[1] if len(names) > 1 else ""
            
            keycloak_user_data = {
                "email": user_data.email,
                "username": user_data.username,
                "password": user_data.password,
                "first_name": first_name,
                "last_name": last_name
            }
            
            keycloak_user_id = await self.keycloak_service.create_user(keycloak_user_data)
            
            # Then create user record in local database
            db_user = User(
                keycloak_id=keycloak_user_id,
                full_name=user_data.full_name,
                email=user_data.email,
                username=user_data.username,
                role_id=user_data.role_id,
                tenant_id=user_data.tenant_id
            )
            
            self.db.add(db_user)
            self.db.commit()
            self.db.refresh(db_user)
            
            return UserResponse(
                id=db_user.id,
                keycloak_id=db_user.keycloak_id,
                full_name=db_user.full_name,
                email=db_user.email,
                username=db_user.username,
                role_id=db_user.role_id,
                tenant_id=db_user.tenant_id,
                created_at=db_user.created_at,
                updated_at=db_user.updated_at
            )
            
        except HTTPException as he:
            # This is already an HTTPException from Keycloak service
            self.db.rollback()
            import logging
            logger = logging.getLogger("uvicorn.error")
            logger.error(f"Keycloak error during user creation: {he.detail}")
            raise HTTPException(status_code=400, detail=f"Failed to create user: {he.detail}")
        except Exception as e:
            # If local DB creation fails, cleanup Keycloak user
            if 'keycloak_user_id' in locals():
                try:
                    await self.keycloak_service.delete_user(keycloak_user_id)
                except:
                    pass  # Log this error but don't fail the response
            
            self.db.rollback()
            import logging
            logger = logging.getLogger("uvicorn.error")
            logger.error(f"General error during user creation: {str(e)}")
            raise HTTPException(status_code=400, detail=f"Failed to create user: {str(e)}")
    
    async def get_user_by_id(self, user_id: str) -> UserResponse:
        """Get user by local database ID"""
        db_user = self.db.query(User).filter(User.id == user_id).first()
        if not db_user:
            raise HTTPException(status_code=404, detail="User not found")
        
        return UserResponse(
            id=db_user.id,
            full_name=db_user.full_name,
            email=db_user.email,
            username=db_user.username,
            role_id=db_user.role_id,
            created_at=db_user.created_at,
            updated_at=db_user.updated_at
        )
    
    async def get_user_by_keycloak_id(self, keycloak_id: str) -> UserResponse:
        """Get user by Keycloak ID"""
        db_user = self.db.query(User).filter(User.keycloak_id == keycloak_id).first()
        if not db_user:
            raise HTTPException(status_code=404, detail="User not found")
        
        return UserResponse(
            id=db_user.id,
            full_name=db_user.full_name,
            email=db_user.email,
            username=db_user.username,
            role_id=db_user.role_id,
            tenant_id=db_user.tenant_id,
            created_at=db_user.created_at,
            updated_at=db_user.updated_at
        )
    
    async def get_users(self, skip: int = 0, limit: int = 100, tenant_id: str = None, is_super_admin: bool = False) -> List[UserResponse]:
        """Get users from local database with proper tenant filtering"""
        query = self.db.query(User)
        
        # Apply tenant filtering unless user is super admin
        if not is_super_admin and tenant_id is not None:
            query = query.filter(User.tenant_id == tenant_id)
        
        users = query.offset(skip).limit(limit).all()
        
        return [
            UserResponse(
                id=user.id,
                keycloak_id=user.keycloak_id,
                full_name=user.full_name,
                email=user.email,
                username=user.username,
                role_id=user.role_id,
                tenant_id=user.tenant_id,
                created_at=user.created_at,
                updated_at=user.updated_at
            )
            for user in users
        ]
    
    async def update_user(self, user_id: str, user_data: UserUpdate) -> UserResponse:
        """Update user in both Keycloak and local database"""
        # Get user from local database
        db_user = self.db.query(User).filter(User.id == user_id).first()
        if not db_user:
            raise HTTPException(status_code=404, detail="User not found")
        
        try:
            # Update in Keycloak
            keycloak_update_data = {}
            if user_data.full_name:
                names = user_data.full_name.split(" ", 1)
                keycloak_update_data["firstName"] = names[0] if names else ""
                keycloak_update_data["lastName"] = names[1] if len(names) > 1 else ""
            
            if user_data.email:
                keycloak_update_data["email"] = user_data.email
            
            if keycloak_update_data:
                await self.keycloak_service.update_user(db_user.keycloak_id, keycloak_update_data)
            
            # Update in local database
            if user_data.full_name is not None:
                db_user.full_name = user_data.full_name
            if user_data.email is not None:
                db_user.email = user_data.email
            if user_data.role_id is not None:
                db_user.role_id = user_data.role_id
            if user_data.tenant_id is not None:
                db_user.tenant_id = user_data.tenant_id
            
            self.db.commit()
            self.db.refresh(db_user)
            
            return UserResponse(
                id=db_user.id,
                keycloak_id=db_user.keycloak_id,
                full_name=db_user.full_name,
                email=db_user.email,
                username=db_user.username,
                role_id=db_user.role_id,
                tenant_id=db_user.tenant_id,
                created_at=db_user.created_at,
                updated_at=db_user.updated_at
            )
            
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=400, detail=f"Failed to update user: {str(e)}")
    
    async def delete_user(self, user_id: str) -> bool:
        """Delete user from both Keycloak and local database"""
        # Get user from local database
        db_user = self.db.query(User).filter(User.id == user_id).first()
        if not db_user:
            raise HTTPException(status_code=404, detail="User not found")
        
        try:
            # Delete from Keycloak
            await self.keycloak_service.delete_user(db_user.keycloak_id)
            
            # Delete from local database
            self.db.delete(db_user)
            self.db.commit()
            
            return True
            
        except Exception as e:
            self.db.rollback()
            raise HTTPException(status_code=400, detail=f"Failed to delete user: {str(e)}")
    
    async def get_total_users_count(self, tenant_id: str = None, is_super_admin: bool = False) -> int:
        """Get total count of users in local database with proper tenant filtering"""
        query = self.db.query(User)
        
        # Apply tenant filtering unless user is super admin
        if not is_super_admin and tenant_id is not None:
            query = query.filter(User.tenant_id == tenant_id)
            
        return query.count()


def get_user_service(db: Session = Depends(get_db)) -> UserService:
    return UserService(db)