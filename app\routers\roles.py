from fastapi import APIRouter, Depends
from typing import List
from ..schemas.role import RoleCreate, RoleResponse
from ..services.role_service import RoleService
from ..middleware.auth import get_current_user

router = APIRouter(prefix="/roles", tags=["roles"])

@router.get("/", response_model=List[RoleResponse])
async def get_roles(
    role_service: RoleService = Depends(),
    current_user: dict = Depends(get_current_user)
):
    roles = role_service.get_roles()
    return [RoleResponse.model_validate(role) for role in roles]

@router.post("/", response_model=RoleResponse)
async def create_role(
    role_data: RoleCreate,
    role_service: RoleService = Depends(),
    current_user: dict = Depends(get_current_user)
):
    role = role_service.create_role(role_data.model_dump())
    return RoleResponse.model_validate(role)