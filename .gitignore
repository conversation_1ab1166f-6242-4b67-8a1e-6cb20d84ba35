# ==================================
# Python
# ==================================
__pycache__/
*.py[cod]
*.pyc
*$py.class

# Virtual environment
.venv/
env/
venv/
ENV/

# Distribution / packaging
build/
dist/
.eggs/
*.egg-info/
.installed.cfg
*.egg

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
pytest_cache/
nosetests.xml
coverage.xml
*.cover
*.py,cover

# Jupyter Notebook
.ipynb_checkpoints

# ==================================
# FastAPI / Uvicorn / Logs
# ==================================
*.log
*.pid
*.db
*.sqlite3

# ==================================
# IDEs / Editors
# ==================================
.vscode/
.idea/
*.swp
*.swo
*.swn
*.bak

# ==================================
# Docker
# ==================================
*.pid
*.seed
*.tar
*.log
docker-compose.override.yml
.docker/

# ==================================
# System
# ==================================
.DS_Store
Thumbs.db

# ==================================
# Custom
# ==================================
# Ignore local config files
.env
.env.*
!env.example
app/routers/__pycache__/permissions.cpython-311.pyc
.claude/settings.local.json
nul
